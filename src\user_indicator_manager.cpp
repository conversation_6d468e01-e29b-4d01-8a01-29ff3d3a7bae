#include "user_indicator_manager.h"
#include <algorithm>
#include <cmath>
#include <iostream>

UserIndicatorManager::UserIndicatorManager()
    : basePosition_(4.0f, FIXED_Y_POSITION)
    , windowWidth_(1920)
    , windowHeight_(1080) {
}

UserIndicatorManager::~UserIndicatorManager() {
}

void UserIndicatorManager::Initialize() {
    userIndicators_.clear();
}

void UserIndicatorManager::Update() {
    for (auto& userIndicator : userIndicators_) {
        if (userIndicator.isActive) {
            // Update animation state
            if (userIndicator.animation.IsFinished()) {
                userIndicator.animation.Stop();
            }
        }
    }
}

void UserIndicatorManager::Render(OpenGLRenderer& renderer) {
    for (auto& userIndicator : userIndicators_) {
        if (userIndicator.isActive) {
            // Get current position (with animation offset)
            Vec2 currentPos = userIndicator.animation.GetCurrentPosition();

            // Render the user indicator as a colored rectangle with border
            Color borderColor(0.2f, 0.2f, 0.2f, 0.8f);
            renderer.DrawRectWithBorder(currentPos, userIndicator.size,
                                      userIndicator.userColor, borderColor, 1.0f);

            // Draw username text using OpenGL text rendering
            if (!userIndicator.username.empty()) {
                // Use a fixed, smaller text scale that matches the size calculation
                float text_scale = 0.7f; // Match the scale used in CalculateOptimalSize
                Vec2 text_size = renderer.GetTextSize(userIndicator.username, text_scale);

                // Center text horizontally and vertically in the indicator
                Vec2 text_pos(
                    currentPos.x + (userIndicator.size.x - text_size.x) * 0.5f,
                    currentPos.y + (userIndicator.size.y - text_size.y) * 0.5f + text_size.y * 0.1f // Slight downward adjustment
                );

                // Calculate text color based on background brightness
                float brightness = (userIndicator.userColor.r * 0.299f +
                                  userIndicator.userColor.g * 0.587f +
                                  userIndicator.userColor.b * 0.114f);

                // Use dark text on bright backgrounds, light text on dark backgrounds
                Color text_color = brightness > 0.5f ?
                    Color(0.0f, 0.0f, 0.0f, 1.0f) :      // Dark text
                    Color(1.0f, 1.0f, 1.0f, 1.0f);       // Light text

                renderer.DrawText(userIndicator.username, text_pos, text_color, text_scale);
            }
        }
    }
}

void UserIndicatorManager::AddUser(const UserInfo& user, OpenGLRenderer& renderer) {
    // Check if user already exists
    auto it = std::find_if(userIndicators_.begin(), userIndicators_.end(),
        [&user](const UserIndicator& indicator) {
            return indicator.userId == user.id;
        });
    
    if (it != userIndicators_.end()) {
        // Update existing user
        it->username = user.username;
        it->isActive = true;
        // Recalculate size in case username changed
        it->CalculateOptimalSize(renderer);
        // Update positions since size might have changed
        UpdateIndicatorPositions();
        return;
    }

    // Generate a unique color for this user
    Color userColor = GenerateUserColor(user.id);
    
    // Create new user indicator
    UserIndicator newIndicator(user.id, user.username, userColor);

    // Calculate optimal size based on username
    newIndicator.CalculateOptimalSize(renderer);

    userIndicators_.push_back(newIndicator);

    // Update positions for all indicators
    UpdateIndicatorPositions();
}

void UserIndicatorManager::RemoveUser(const std::string& userId) {
    auto it = std::find_if(userIndicators_.begin(), userIndicators_.end(),
        [&userId](const UserIndicator& indicator) {
            return indicator.userId == userId;
        });
    
    if (it != userIndicators_.end()) {
        userIndicators_.erase(it);
        UpdateIndicatorPositions();
    }
}

void UserIndicatorManager::UpdateUsers(const std::vector<UserInfo>& users, OpenGLRenderer& renderer) {
    // Mark all current users as inactive
    for (auto& indicator : userIndicators_) {
        indicator.isActive = false;
    }

    // Add or update users from the new list
    for (const auto& user : users) {
        AddUser(user, renderer);
    }

    // Remove inactive users
    userIndicators_.erase(
        std::remove_if(userIndicators_.begin(), userIndicators_.end(),
            [](const UserIndicator& indicator) {
                return !indicator.isActive;
            }),
        userIndicators_.end()
    );

    UpdateIndicatorPositions();
}

void UserIndicatorManager::ClearUsers() {
    userIndicators_.clear();
}

void UserIndicatorManager::TriggerUserAnimation(const std::string& userId) {
    auto it = std::find_if(userIndicators_.begin(), userIndicators_.end(),
        [&userId](UserIndicator& indicator) {
            return indicator.userId == userId;
        });

    if (it != userIndicators_.end()) {
        it->animation.Start(it->position);
    }
}

UserIndicator* UserIndicatorManager::GetUserIndicator(const std::string& userId) {
    auto it = std::find_if(userIndicators_.begin(), userIndicators_.end(),
        [&userId](const UserIndicator& indicator) {
            return indicator.userId == userId;
        });
    
    return (it != userIndicators_.end()) ? &(*it) : nullptr;
}

const std::vector<UserIndicator>& UserIndicatorManager::GetUserIndicators() const {
    return userIndicators_;
}

void UserIndicatorManager::SetBasePosition(const Vec2& basePos) {
    basePosition_ = Vec2(basePos.x, FIXED_Y_POSITION); // Y is always fixed
    UpdateIndicatorPositions();
}

void UserIndicatorManager::SetWindowDimensions(int width, int height) {
    windowWidth_ = width;
    windowHeight_ = height;
    UpdateIndicatorPositions(); // Recalculate positions with new window size
}

Color UserIndicatorManager::GenerateUserColor(const std::string& userId) {
    // Generate a consistent color based on user ID hash
    std::hash<std::string> hasher;
    size_t hash = hasher(userId);
    
    // Use hash to generate HSV color, then convert to RGB
    float hue = (hash % 360) / 360.0f;
    float saturation = 0.7f + (hash % 30) / 100.0f; // 0.7 - 1.0
    float value = 0.8f + (hash % 20) / 100.0f;      // 0.8 - 1.0
    
    // Convert HSV to RGB
    float c = value * saturation;
    float x = c * (1.0f - std::abs(std::fmod(hue * 6.0f, 2.0f) - 1.0f));
    float m = value - c;
    
    float r, g, b;
    if (hue < 1.0f/6.0f) {
        r = c; g = x; b = 0;
    } else if (hue < 2.0f/6.0f) {
        r = x; g = c; b = 0;
    } else if (hue < 3.0f/6.0f) {
        r = 0; g = c; b = x;
    } else if (hue < 4.0f/6.0f) {
        r = 0; g = x; b = c;
    } else if (hue < 5.0f/6.0f) {
        r = x; g = 0; b = c;
    } else {
        r = c; g = 0; b = x;
    }
    
    return Color(r + m, g + m, b + m, 1.0f);
}

void UserIndicatorManager::UpdateIndicatorPositions() {
    if (userIndicators_.empty()) return;

    float current_x = basePosition_.x;
    float current_y = basePosition_.y;
    float row_height = 0.0f;

    // Use actual window width for wrapping calculation
    float window_width = static_cast<float>(windowWidth_);

    // Calculate margin from right edge
    float right_margin = 20.0f;
    float max_x = window_width - right_margin;

    for (size_t i = 0; i < userIndicators_.size(); ++i) {
        float indicator_width = userIndicators_[i].size.x;
        float indicator_height = userIndicators_[i].size.y;

        // Check if this indicator would go beyond the screen width
        if (current_x + indicator_width > max_x && i > 0) {
            // Move to next row
            current_x = basePosition_.x;
            current_y += row_height + INDICATOR_SPACING;
            row_height = 0.0f;
        }

        // Set position for this indicator
        Vec2 position(current_x, current_y);
        userIndicators_[i].position = position;
        userIndicators_[i].animation.originalPosition = position;

        // Update row height to accommodate the tallest indicator in this row
        row_height = std::max(row_height, indicator_height);

        // Move to next position
        current_x += indicator_width + INDICATOR_SPACING;
    }
}

void UserIndicator::CalculateOptimalSize(OpenGLRenderer& renderer) {
    if (username.empty()) {
        size = Vec2(60.0f, 22.0f); // Default size for empty username
        return;
    }

    // Calculate text size with the same scale that will be used for rendering
    float text_scale = 0.7f;
    Vec2 text_size = renderer.GetTextSize(username, text_scale);

    // Debug output
    std::cout << "Username: '" << username << "' (length: " << username.length()
              << ") -> text_size: " << text_size.x << "x" << text_size.y << std::endl;

    // Add padding around the text
    float padding_x = 12.0f; // 6px on each side
    float padding_y = 6.0f;  // 3px on top and bottom

    // Calculate indicator size based entirely on text dimensions
    float optimal_width = text_size.x + padding_x;
    float optimal_height = text_size.y + padding_y;

    // Only set minimum sizes, no maximum width limit for long names
    float min_width = 50.0f;  // Minimum for very short names
    float min_height = 20.0f; // Minimum height
    float max_height = 28.0f; // Maximum height to keep consistent

    // Apply minimum constraints but allow width to grow with name length
    optimal_width = std::max(min_width, optimal_width);
    optimal_height = std::max(min_height, std::min(max_height, optimal_height));

    size = Vec2(optimal_width, optimal_height);

    std::cout << "Final size: " << size.x << "x" << size.y << std::endl;
}
