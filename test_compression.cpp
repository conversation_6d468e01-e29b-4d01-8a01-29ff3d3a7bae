#include <iostream>
#include <vector>
#include <cstring>
#include <zlib.h>

// Simple compression test function
std::vector<uint8_t> compressData(const std::vector<uint8_t>& data) {
    if (data.empty()) {
        return {};
    }

    z_stream stream;
    memset(&stream, 0, sizeof(stream));
    
    if (deflateInit(&stream, Z_DEFAULT_COMPRESSION) != Z_OK) {
        std::cerr << "Failed to initialize zlib compression" << std::endl;
        return {};
    }

    stream.avail_in = static_cast<uInt>(data.size());
    stream.next_in = const_cast<Bytef*>(data.data());

    std::vector<uint8_t> compressed;
    compressed.resize(data.size() + 12);
    
    stream.avail_out = static_cast<uInt>(compressed.size());
    stream.next_out = compressed.data();

    int result = deflate(&stream, Z_FINISH);
    
    if (result != Z_STREAM_END) {
        std::cerr << "Failed to compress data: " << result << std::endl;
        deflateEnd(&stream);
        return {};
    }

    compressed.resize(compressed.size() - stream.avail_out);
    deflateEnd(&stream);
    return compressed;
}

std::vector<uint8_t> decompressData(const std::vector<uint8_t>& compressedData) {
    if (compressedData.empty()) {
        return {};
    }

    z_stream stream;
    memset(&stream, 0, sizeof(stream));
    
    if (inflateInit(&stream) != Z_OK) {
        std::cerr << "Failed to initialize zlib decompression" << std::endl;
        return {};
    }

    stream.avail_in = static_cast<uInt>(compressedData.size());
    stream.next_in = const_cast<Bytef*>(compressedData.data());

    std::vector<uint8_t> decompressed;
    decompressed.resize(compressedData.size() * 4);
    
    stream.avail_out = static_cast<uInt>(decompressed.size());
    stream.next_out = decompressed.data();

    int result = inflate(&stream, Z_FINISH);
    
    if (result != Z_STREAM_END) {
        if (result == Z_BUF_ERROR) {
            decompressed.resize(compressedData.size() * 10);
            stream.avail_out = static_cast<uInt>(decompressed.size() - (decompressed.size() - stream.avail_out));
            stream.next_out = decompressed.data() + (decompressed.size() - stream.avail_out);
            
            result = inflate(&stream, Z_FINISH);
        }
        
        if (result != Z_STREAM_END) {
            std::cerr << "Failed to decompress data: " << result << std::endl;
            inflateEnd(&stream);
            return {};
        }
    }

    decompressed.resize(decompressed.size() - stream.avail_out);
    inflateEnd(&stream);
    return decompressed;
}

int main() {
    std::cout << "Testing zlib compression/decompression..." << std::endl;
    
    // Create test MIDI data (simulating multiple MidiBuffer structs)
    std::vector<uint8_t> testData;
    
    // Add 100 MIDI note events (8 bytes each)
    for (int i = 0; i < 100; i++) {
        testData.push_back(144);  // Note On status
        testData.push_back(60 + (i % 12));  // Note number
        testData.push_back(100);  // Velocity
        testData.push_back(255);  // Red
        testData.push_back(128);  // Green
        testData.push_back(64);   // Blue
        testData.push_back(0);    // Delta high
        testData.push_back(i % 256);  // Delta low
    }
    
    std::cout << "Original data size: " << testData.size() << " bytes" << std::endl;
    
    // Compress the data
    auto compressed = compressData(testData);
    if (compressed.empty()) {
        std::cerr << "Compression failed!" << std::endl;
        return 1;
    }
    
    std::cout << "Compressed data size: " << compressed.size() << " bytes" << std::endl;
    std::cout << "Compression ratio: " << (100.0 * compressed.size() / testData.size()) << "%" << std::endl;
    
    // Decompress the data
    auto decompressed = decompressData(compressed);
    if (decompressed.empty()) {
        std::cerr << "Decompression failed!" << std::endl;
        return 1;
    }
    
    std::cout << "Decompressed data size: " << decompressed.size() << " bytes" << std::endl;
    
    // Verify data integrity
    if (testData.size() != decompressed.size()) {
        std::cerr << "Size mismatch after decompression!" << std::endl;
        return 1;
    }
    
    for (size_t i = 0; i < testData.size(); i++) {
        if (testData[i] != decompressed[i]) {
            std::cerr << "Data mismatch at byte " << i << std::endl;
            return 1;
        }
    }
    
    std::cout << "✓ Compression/decompression test passed!" << std::endl;
    std::cout << "✓ Data integrity verified!" << std::endl;
    
    return 0;
}
