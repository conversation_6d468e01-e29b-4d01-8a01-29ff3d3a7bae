#pragma once

#include <string>
#include <vector>
#include <filesystem>

class NativeFileDialog {
public:
    struct Filter {
        std::string name;
        std::string pattern;
    };

    NativeFileDialog() = default;
    ~NativeFileDialog() = default;

    void SetTitle(const std::string& title);
    void SetTypeFilters(const std::vector<std::string>& extensions);
    bool OpenDialog(bool save = false, bool multiSelect = false);
    bool HasSelected() const;
    std::filesystem::path GetSelected() const;
    std::vector<std::filesystem::path> GetMultiSelected() const;
    void ClearSelected();

private:
    std::string title_;
    std::vector<Filter> filters_;
    std::filesystem::path selected_path_;
    std::vector<std::filesystem::path> selected_paths_;
    bool has_selected_ = false;
    
#ifdef _WIN32
    std::string BuildFilterString() const;
#endif
};
