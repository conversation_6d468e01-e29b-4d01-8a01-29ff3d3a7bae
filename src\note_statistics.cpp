#include "note_statistics.h"
#include <algorithm>
#include <numeric>

NoteStatistics::NoteStatistics()
    : total_notes_played_(0)
    , current_nps_(0.0f)
    , max_nps_(0.0f)
    , average_nps_(0.0f)
    , max_history_size_(200) // More data points for smoother graph
    , update_interval_seconds_(0.05f) // Update every 50ms for smoother updates
{
}

NoteStatistics::~NoteStatistics() {
}

void NoteStatistics::Initialize() {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    
    start_time_ = std::chrono::steady_clock::now();
    last_update_time_ = start_time_;
    last_nps_calculation_time_ = start_time_;
    
    total_notes_played_ = 0;
    current_nps_ = 0.0f;
    max_nps_ = 0.0f;
    average_nps_ = 0.0f;
    
    recent_note_times_.clear();
    nps_history_.clear();
    nps_history_.reserve(max_history_size_);
}

void NoteStatistics::Update() {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    auto time_since_last_update = std::chrono::duration<float>(now - last_nps_calculation_time_).count();
    
    // Update NPS calculation at specified interval
    if (time_since_last_update >= update_interval_seconds_) {
        CalculateNPS();
        UpdateHistory();
        last_nps_calculation_time_ = now;
    }
    
    // Clean old note times (older than 1 second for NPS calculation)
    CleanOldNotes();
    
    last_update_time_ = now;
}

void NoteStatistics::RecordNotePlayed() {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    
    total_notes_played_++;
    recent_note_times_.push_back(std::chrono::steady_clock::now());
}

int NoteStatistics::GetTotalNotesPlayed() const {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    return total_notes_played_;
}

float NoteStatistics::GetCurrentNPS() const {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    return current_nps_;
}

const std::vector<float>& NoteStatistics::GetNPSHistory() const {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    return nps_history_;
}

float NoteStatistics::GetMaxNPS() const {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    return max_nps_;
}

float NoteStatistics::GetAverageNPS() const {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    return average_nps_;
}

void NoteStatistics::Reset() {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    
    total_notes_played_ = 0;
    current_nps_ = 0.0f;
    max_nps_ = 0.0f;
    average_nps_ = 0.0f;
    
    recent_note_times_.clear();
    nps_history_.clear();
    
    start_time_ = std::chrono::steady_clock::now();
    last_update_time_ = start_time_;
    last_nps_calculation_time_ = start_time_;
}

void NoteStatistics::SetHistorySize(int size) {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    max_history_size_ = std::max(10, size); // Minimum 10 entries
    nps_history_.reserve(max_history_size_);
    
    // Trim history if it's too large
    if (static_cast<int>(nps_history_.size()) > max_history_size_) {
        nps_history_.erase(nps_history_.begin(), 
                          nps_history_.begin() + (nps_history_.size() - max_history_size_));
    }
}

void NoteStatistics::SetUpdateInterval(float interval_seconds) {
    std::lock_guard<std::mutex> lock(statistics_mutex_);
    update_interval_seconds_ = std::max(0.01f, interval_seconds); // Minimum 10ms
}

void NoteStatistics::CalculateNPS() {
    auto now = std::chrono::steady_clock::now();

    // Count notes in different time windows for smoother calculation
    int notes_in_last_500ms = 0;
    int notes_in_last_1000ms = 0;

    for (const auto& note_time : recent_note_times_) {
        auto time_diff = std::chrono::duration<float>(now - note_time).count();
        if (time_diff <= 0.5f) {
            notes_in_last_500ms++;
        }
        if (time_diff <= 1.0f) {
            notes_in_last_1000ms++;
        }
    }

    // Use weighted average for smoother NPS calculation
    float nps_500ms = static_cast<float>(notes_in_last_500ms) * 2.0f; // Scale to per-second
    float nps_1000ms = static_cast<float>(notes_in_last_1000ms);

    // Weighted average: 70% recent (500ms), 30% longer term (1000ms)
    float new_nps = (nps_500ms * 0.7f) + (nps_1000ms * 0.3f);

    // Apply smoothing to reduce jitter
    current_nps_ = (current_nps_ * 0.8f) + (new_nps * 0.2f);

    // Update max NPS
    if (current_nps_ > max_nps_) {
        max_nps_ = current_nps_;
    }

    // Calculate average NPS over the entire session
    auto session_duration = std::chrono::duration<float>(now - start_time_).count();
    if (session_duration > 0.0f) {
        average_nps_ = static_cast<float>(total_notes_played_) / session_duration;
    }
}

void NoteStatistics::UpdateHistory() {
    nps_history_.push_back(current_nps_);
    
    // Keep history size within limits
    if (static_cast<int>(nps_history_.size()) > max_history_size_) {
        nps_history_.erase(nps_history_.begin());
    }
}

void NoteStatistics::CleanOldNotes() {
    auto now = std::chrono::steady_clock::now();
    
    // Remove notes older than 2 seconds (we only need 1 second for NPS, but keep a bit extra for safety)
    while (!recent_note_times_.empty()) {
        auto time_diff = std::chrono::duration<float>(now - recent_note_times_.front()).count();
        if (time_diff > 2.0f) {
            recent_note_times_.pop_front();
        } else {
            break; // Since deque is ordered by time, we can stop here
        }
    }
}
