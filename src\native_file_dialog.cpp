#include "native_file_dialog.h"

#ifdef _WIN32
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <commdlg.h>
#include <shobjidl.h>
#include <objbase.h>
#include <string>
#include <sstream>

void NativeFileDialog::SetTitle(const std::string& title) {
    title_ = title;
}

void NativeFileDialog::SetTypeFilters(const std::vector<std::string>& extensions) {
    filters_.clear();
    
    // Convert extensions to Windows file dialog format
    for (const auto& ext : extensions) {
        Filter filter;
        std::string cleanExt = ext;
        
        // Remove the dot if present
        if (cleanExt.front() == '.') {
            cleanExt = cleanExt.substr(1);
        }
        
        // Convert to uppercase for display name
        std::string upperExt = cleanExt;
        for (auto& c : upperExt) {
            c = std::toupper(c);
        }
        
        filter.name = upperExt + " Files (*." + cleanExt + ")";
        filter.pattern = "*." + cleanExt;
        filters_.push_back(filter);
    }
    
    // Add "All Files" filter
    if (!filters_.empty()) {
        Filter allFilter;
        allFilter.name = "All Files (*.*)";
        allFilter.pattern = "*.*";
        filters_.push_back(allFilter);
    }
}

std::string NativeFileDialog::BuildFilterString() const {
    std::string filterStr;
    
    for (const auto& filter : filters_) {
        filterStr += filter.name + '\0' + filter.pattern + '\0';
    }
    
    filterStr += '\0';  // Double null terminator
    return filterStr;
}

bool NativeFileDialog::OpenDialog(bool save, bool multiSelect) {
    has_selected_ = false;
    selected_path_.clear();
    selected_paths_.clear();
    
    if (multiSelect && !save) {
        // Use newer COM interface for multiple selection
        HRESULT hr = CoInitializeEx(NULL, COINIT_APARTMENTTHREADED | COINIT_DISABLE_OLE1DDE);
        if (FAILED(hr)) {
            return false;
        }
        
        IFileOpenDialog* pFileOpen = nullptr;
        hr = CoCreateInstance(CLSID_FileOpenDialog, NULL, CLSCTX_ALL, IID_IFileOpenDialog, reinterpret_cast<void**>(&pFileOpen));
        
        if (SUCCEEDED(hr)) {
            // Set options for multiple selection
            DWORD dwFlags;
            hr = pFileOpen->GetOptions(&dwFlags);
            if (SUCCEEDED(hr)) {
                hr = pFileOpen->SetOptions(dwFlags | FOS_ALLOWMULTISELECT | FOS_FILEMUSTEXIST | FOS_PATHMUSTEXIST);
            }
            
            // Set file type filters
            if (!filters_.empty()) {
                std::vector<COMDLG_FILTERSPEC> filterSpecs;
                std::vector<std::wstring> filterNames;
                std::vector<std::wstring> filterPatterns;
                
                for (const auto& filter : filters_) {
                    filterNames.emplace_back(filter.name.begin(), filter.name.end());
                    filterPatterns.emplace_back(filter.pattern.begin(), filter.pattern.end());
                    
                    COMDLG_FILTERSPEC spec;
                    spec.pszName = filterNames.back().c_str();
                    spec.pszSpec = filterPatterns.back().c_str();
                    filterSpecs.push_back(spec);
                }
                
                hr = pFileOpen->SetFileTypes(static_cast<UINT>(filterSpecs.size()), filterSpecs.data());
            }
            
            // Set title
            if (!title_.empty()) {
                std::wstring wTitle(title_.begin(), title_.end());
                hr = pFileOpen->SetTitle(wTitle.c_str());
            }
            
            // Show the dialog
            hr = pFileOpen->Show(NULL);
            
            if (SUCCEEDED(hr)) {
                IShellItemArray* pItems = nullptr;
                hr = pFileOpen->GetResults(&pItems);
                
                if (SUCCEEDED(hr)) {
                    DWORD itemCount = 0;
                    hr = pItems->GetCount(&itemCount);
                    
                    if (SUCCEEDED(hr) && itemCount > 0) {
                        for (DWORD i = 0; i < itemCount; i++) {
                            IShellItem* pItem = nullptr;
                            hr = pItems->GetItemAt(i, &pItem);
                            
                            if (SUCCEEDED(hr)) {
                                PWSTR pszFilePath = nullptr;
                                hr = pItem->GetDisplayName(SIGDN_FILESYSPATH, &pszFilePath);
                                
                                if (SUCCEEDED(hr)) {
                                    // Convert wide string to UTF-8
                                    int utf8Size = WideCharToMultiByte(CP_UTF8, 0, pszFilePath, -1, nullptr, 0, nullptr, nullptr);
                                    if (utf8Size > 0) {
                                        std::string utf8Path(utf8Size - 1, '\0');
                                        WideCharToMultiByte(CP_UTF8, 0, pszFilePath, -1, &utf8Path[0], utf8Size, nullptr, nullptr);
                                        
                                        selected_paths_.push_back(std::filesystem::path(utf8Path));
                                    }
                                    CoTaskMemFree(pszFilePath);
                                }
                                pItem->Release();
                            }
                        }
                        
                        if (!selected_paths_.empty()) {
                            selected_path_ = selected_paths_[0]; // Set first item as primary selection
                            has_selected_ = true;
                        }
                    }
                    pItems->Release();
                }
            }
            
            pFileOpen->Release();
        }
        
        CoUninitialize();
        return has_selected_;
    } else {
        // Single selection using legacy API
        OPENFILENAMEA ofn;
        char szFile[MAX_PATH] = "";
        
        ZeroMemory(&ofn, sizeof(ofn));
        ofn.lStructSize = sizeof(ofn);
        ofn.hwndOwner = nullptr;
        ofn.lpstrFile = szFile;
        ofn.nMaxFile = sizeof(szFile);
        
        // Build filter string
        std::string filterStr = BuildFilterString();
        ofn.lpstrFilter = filterStr.empty() ? nullptr : filterStr.c_str();
        
        ofn.nFilterIndex = 1;
        ofn.lpstrFileTitle = nullptr;
        ofn.nMaxFileTitle = 0;
        ofn.lpstrInitialDir = nullptr;
        
        // Set title
        ofn.lpstrTitle = title_.empty() ? nullptr : title_.c_str();
        
        ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST | OFN_NOCHANGEDIR;
        
        BOOL result;
        if (save) {
            result = GetSaveFileNameA(&ofn);
        } else {
            result = GetOpenFileNameA(&ofn);
        }
        
        if (result) {
            selected_path_ = std::filesystem::path(szFile);
            selected_paths_ = {selected_path_}; // Single item in vector
            has_selected_ = true;
            return true;
        }
        
        return false;
    }
}

bool NativeFileDialog::HasSelected() const {
    return has_selected_;
}

std::filesystem::path NativeFileDialog::GetSelected() const {
    return selected_path_;
}

std::vector<std::filesystem::path> NativeFileDialog::GetMultiSelected() const {
    return selected_paths_;
}

void NativeFileDialog::ClearSelected() {
    has_selected_ = false;
    selected_path_.clear();
    selected_paths_.clear();
}

#else
// Non-Windows implementations (fallback to ImGui browser or other methods)

void NativeFileDialog::SetTitle(const std::string& title) {
    title_ = title;
}

void NativeFileDialog::SetTypeFilters(const std::vector<std::string>& extensions) {
    // Implementation for other platforms can be added here
}

bool NativeFileDialog::OpenDialog(bool save, bool multiSelect) {
    // Implementation for other platforms can be added here
    return false;
}

bool NativeFileDialog::HasSelected() const {
    return has_selected_;
}

std::filesystem::path NativeFileDialog::GetSelected() const {
    return selected_path_;
}

std::vector<std::filesystem::path> NativeFileDialog::GetMultiSelected() const {
    return selected_paths_;
}

void NativeFileDialog::ClearSelected() {
    has_selected_ = false;
    selected_path_.clear();
    selected_paths_.clear();
}

#endif
