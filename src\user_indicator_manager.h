#pragma once

#include <vector>
#include <map>
#include <string>
#include <chrono>
#include "opengl_renderer.h"
#include "websocket_client.h"

// Simple animation structure for user indicators
struct UserIndicatorAnimation {
    bool isAnimating;
    std::chrono::steady_clock::time_point startTime;
    float duration;  // Animation duration in milliseconds
    float bounceDistance;  // How far to move during animation
    Vec2 originalPosition;

    UserIndicatorAnimation()
        : isAnimating(false), duration(200.0f), bounceDistance(8.0f) {}

    void Start(const Vec2& position) {
        isAnimating = true;
        startTime = std::chrono::steady_clock::now();
        originalPosition = position;
    }

    void Stop() {
        isAnimating = false;
    }

    Vec2 GetCurrentPosition() const {
        if (!isAnimating) return originalPosition;

        auto now = std::chrono::steady_clock::now();
        float elapsed = std::chrono::duration<float, std::milli>(now - startTime).count();

        if (elapsed >= duration) {
            return originalPosition;
        }

        // Simple bounce animation
        float progress = elapsed / duration;
        float bounce = sin(progress * 3.14159f) * bounceDistance;
        return Vec2(originalPosition.x, originalPosition.y - bounce);
    }

    bool IsFinished() const {
        if (!isAnimating) return true;

        auto now = std::chrono::steady_clock::now();
        float elapsed = std::chrono::duration<float, std::milli>(now - startTime).count();
        return elapsed >= duration;
    }
};

struct UserIndicator {
    std::string userId;
    std::string username;
    Color userColor;
    bool isActive;

    // Visual properties
    Vec2 position;
    Vec2 size;
    UserIndicatorAnimation animation;

    UserIndicator(const std::string& id, const std::string& name, const Color& color)
        : userId(id), username(name), userColor(color), isActive(true) {
        // Default size for user indicators (will be adjusted based on text)
        size = Vec2(125.0f, 25.0f);  // Default w, h
        animation.duration = 120.0f;  // Fixed animation duration
        animation.bounceDistance = 6.0f;  // Fixed bounce distance
    }

    // Calculate optimal size based on username
    void CalculateOptimalSize(class OpenGLRenderer& renderer);
};

class UserIndicatorManager {
public:
    UserIndicatorManager();
    ~UserIndicatorManager();

    // Initialize the manager
    void Initialize();

    // Update all indicators
    void Update();

    // Render all indicators
    void Render(OpenGLRenderer& renderer);

    // User management
    void AddUser(const UserInfo& user, OpenGLRenderer& renderer);
    void RemoveUser(const std::string& userId);
    void UpdateUsers(const std::vector<UserInfo>& users, OpenGLRenderer& renderer);
    void ClearUsers();

    // Trigger animation for a specific user
    void TriggerUserAnimation(const std::string& userId);

    // Get user indicator by ID
    UserIndicator* GetUserIndicator(const std::string& userId);

    // Get all user indicators
    const std::vector<UserIndicator>& GetUserIndicators() const;

    // Set base position (y is fixed, x will be calculated for each user)
    void SetBasePosition(const Vec2& basePos);

    // Set window dimensions for wrapping calculation
    void SetWindowDimensions(int width, int height);

private:
    std::vector<UserIndicator> userIndicators_;
    Vec2 basePosition_;
    int windowWidth_;
    int windowHeight_;
    
    // Fixed values
    static constexpr float INDICATOR_WIDTH = 125.0f;
    static constexpr float INDICATOR_HEIGHT = 25.0f;
    static constexpr float INDICATOR_SPACING = 4.0f;
    static constexpr float FIXED_Y_POSITION = 28.0f;

    // Helper functions
    Color GenerateUserColor(const std::string& userId);
    void UpdateIndicatorPositions();
};
