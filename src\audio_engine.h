#pragma once

#include <string>
#include <memory>
#include <vector>

#if defined(_WIN32)
#include <windows.h>
#else
#include <dlfcn.h>
#endif

#define NOBASSMIDIOVERLOADS
#include "bass.h"
#include "bassmidi.h"
#include "bass_fx.h"
#include "alsa_midi.h"
#include "winmm_midi.h"
#include "ext_process_midi.h"
#include "audio_limiter.h"
#include "soundfont_manager.h"
#include "note_statistics.h"

// Forward declarations
class PianoKeyboard;
struct AppConfig;

class AudioEngine {
public:
    AudioEngine();
    ~AudioEngine();

    // Initialize the audio engine
    bool Initialize();

    // Auto-open MIDI device based on configuration
    bool AutoOpenMIDIDevice(const struct AppConfig& config);

    // Cleanup the audio engine
    void Cleanup();
    
    // Load a soundfont file (.sf2 or .sfz) - legacy single soundfont method
    bool LoadSoundfont(const std::string& soundfont_path);

    // SoundFont Manager methods
    SoundFontManager& GetSoundFontManager() { return soundfont_manager_; }
    const SoundFontManager& GetSoundFontManager() const { return soundfont_manager_; }
    bool ApplySoundFonts(); // Apply all enabled soundfonts to MIDI stream
    
    // Play a MIDI note
    void PlayNote(int note, int velocity = 127, bool from_midi_input = false);
    void PlayNote(int note, int velocity, bool from_midi_input, int channel);

    // Stop a MIDI note
    void StopNote(int note, bool from_midi_input = false);
    void StopNote(int note, bool from_midi_input, int channel);

    // Stop all notes
    void StopAllNotes();

    // Process audio for all engines
    void ProcessAudio();
    
    // Set master volume (0.0 to 1.0)
    void SetVolume(float volume);
    
    // Get current volume
    float GetVolume() const;
    
    // Check if audio engine is initialized
    bool IsInitialized() const;

    // Check if soundfont is loaded
    bool IsSoundfontLoaded() const;

    // Get current soundfont path
    const std::string& GetCurrentSoundfontPath() const;

    // Get polyphony information
    int GetCurrentPolyphony() const;
    int GetMaxPolyphony() const;

    // Set maximum polyphony (number of simultaneous voices)
    bool SetMaxPolyphony(int max_polyphony);

    // Test functions
    void PlayTestTone();
    void PlayAllNotes();
    void PrintDeviceInfo();

    // Panic/Recovery functions
    bool PanicRestart();                    // Emergency restart of audio engine
    bool IsAudioWorking() const;            // Check if audio is working properly
    void EmergencyStopAllNotes();           // Force stop all notes immediately

    // Debug information functions
    float GetCPUUsage() const;
    float GetRenderingTime() const;
    int GetActiveChannels() const;
    std::string GetAudioInfo() const;

    // MIDI input functions (BASS-based)
    bool InitializeMIDIInput();
    void CleanupMIDIInput();
    std::vector<std::string> GetMIDIInputDevices() const;
    bool OpenMIDIInputDevice(int device_id);
    void CloseMIDIInputDevice();
    bool IsMIDIInputOpen() const;
    std::string GetCurrentMIDIInputDevice() const;

    // ALSA MIDI input functions (Linux)
    bool InitializeALSAMIDI();
    void CleanupALSAMIDI();
    std::vector<ALSAMIDIDevice> GetALSAMIDIDevices() const;
    bool OpenALSAMIDIDevice(int client_id, int port_id);
    void CloseALSAMIDIDevice();
    bool IsALSAMIDIOpen() const;
    ALSAMIDIDevice GetCurrentALSAMIDIDevice() const;

    // External Process MIDI input functions
    bool InitializeExtProcessMIDI();
    void CleanupExtProcessMIDI();
    bool StartExtProcessMIDI(const std::string& executable_path, const std::vector<std::string>& args);
    void StopExtProcessMIDI();
    bool IsExtProcessMIDIActive() const;
    std::string GetExtProcessMIDIStderrLog() const;
    void ClearExtProcessMIDIStderrLog();

    // Windows MIDI input functions
    bool InitializeWinMMMIDI();
    void CleanupWinMMMIDI();
    std::vector<WinMMMIDIDevice> GetWinMMMIDIDevices() const;
    bool OpenWinMMMIDIDevice(int device_id);
    void CloseWinMMMIDIDevice();
    bool IsWinMMMIDIOpen() const;
    WinMMMIDIDevice GetCurrentWinMMMIDIDevice() const;

    // Piano keyboard integration for visual feedback
    void SetPianoKeyboard(PianoKeyboard* piano_keyboard);
    void NotifyMIDIKeyPressed(int note, bool pressed);
    void NotifyMIDIKeyPressed(int note, bool pressed, int channel);
    void NotifyMIDIKeyPressed(int note, bool pressed, int channel, int velocity);

    // Audio Limiter functions
    AudioLimiter* GetAudioLimiter() { return &audio_limiter_; }
    void SetLimiterEnabled(bool enabled);
    bool IsLimiterEnabled() const;

    // Note Statistics functions
    NoteStatistics* GetNoteStatistics() { return &note_statistics_; }
    const NoteStatistics* GetNoteStatistics() const { return &note_statistics_; }
    void ResetNoteStatistics() { note_statistics_.Reset(); }

    // BASS FX functions
    void SetBASS_FXEnabled(bool enabled);
    bool IsBASS_FXEnabled() const;
    void SetReverbEnabled(bool enabled);
    void SetChorusEnabled(bool enabled);
    void SetEchoEnabled(bool enabled);
    void SetCompressorEnabled(bool enabled);
    void SetReverbMix(float mix);
    void SetChorusMix(float mix);
    void SetEchoMix(float mix);
    void SetCompressorRatio(float ratio);
    bool IsReverbEnabled() const;
    bool IsChorusEnabled() const;
    bool IsEchoEnabled() const;
    bool IsCompressorEnabled() const;





private:
    bool initialized_;
    HSTREAM midi_stream_;
    HSOUNDFONT soundfont_;  // Legacy single soundfont (for backward compatibility)
    float master_volume_;
    std::string current_soundfont_path_;  // Legacy path (for backward compatibility)

    // SoundFont Manager
    SoundFontManager soundfont_manager_;

    // Polyphony tracking
    int max_polyphony_;
    mutable int current_polyphony_;

    // MIDI input (BASS-based)
    HRECORD midi_input_;
    int current_midi_input_device_;
    std::string current_midi_input_name_;

    // Platform-specific MIDI input
    std::unique_ptr<ALSAMIDIInput> alsa_midi_;      // Linux ALSA MIDI
    std::unique_ptr<WinMMMIDIInput> winmm_midi_;    // Windows MIDI
    std::unique_ptr<ExtProcessMIDIInput> ext_process_midi_; // External Process MIDI

    // Piano keyboard for visual feedback
    PianoKeyboard* piano_keyboard_;

    // Audio Limiter
    AudioLimiter audio_limiter_;
    HDSP limiter_dsp_;

    // Note Statistics
    NoteStatistics note_statistics_;

    // BASS FX effects
    bool bassfx_enabled_;
    HFX reverb_fx_;
    HFX chorus_fx_;
    HFX echo_fx_;
    HFX compressor_fx_;

    // BASS FX parameters
    float reverb_mix_;
    float chorus_mix_;
    float echo_mix_;
    float compressor_ratio_;





    // Dynamic library handles
    void* bass_lib_;
    void* bassmidi_lib_;

    // Function pointers using official BASS function signatures
    decltype(&BASS_Init) BASS_Init_ptr;
    decltype(&BASS_Free) BASS_Free_ptr;
    decltype(&BASS_MIDI_StreamCreate) BASS_MIDI_StreamCreate_ptr;
    decltype(&BASS_ChannelPlay) BASS_ChannelPlay_ptr;
    decltype(&BASS_ChannelSetAttribute) BASS_ChannelSetAttribute_ptr;
    decltype(&BASS_StreamFree) BASS_StreamFree_ptr;

    // BASS FX function pointers
    decltype(&BASS_ChannelSetFX) BASS_ChannelSetFX_ptr;
    decltype(&BASS_ChannelRemoveFX) BASS_ChannelRemoveFX_ptr;
    decltype(&BASS_FXSetParameters) BASS_FXSetParameters_ptr;
    decltype(&BASS_FXGetParameters) BASS_FXGetParameters_ptr;
    decltype(&BASS_MIDI_StreamEvent) BASS_MIDI_StreamEvent_ptr;
    decltype(&BASS_MIDI_FontInit) BASS_MIDI_FontInit_ptr;
    decltype(&BASS_MIDI_FontFree) BASS_MIDI_FontFree_ptr;
    decltype(&BASS_MIDI_FontSetVolume) BASS_MIDI_FontSetVolume_ptr;
    decltype(&BASS_MIDI_StreamSetFonts) BASS_MIDI_StreamSetFonts_ptr;
    decltype(&BASS_ErrorGetCode) BASS_ErrorGetCode_ptr;
    decltype(&BASS_ChannelGetAttribute) BASS_ChannelGetAttribute_ptr;
    decltype(&BASS_ChannelIsActive) BASS_ChannelIsActive_ptr;
    decltype(&BASS_GetDeviceInfo) BASS_GetDeviceInfo_ptr;
    decltype(&BASS_SetConfig) BASS_SetConfig_ptr;
    decltype(&BASS_GetCPU) BASS_GetCPU_ptr;
    decltype(&BASS_GetInfo) BASS_GetInfo_ptr;
    decltype(&BASS_RecordGetDeviceInfo) BASS_RecordGetDeviceInfo_ptr;
    decltype(&BASS_RecordInit) BASS_RecordInit_ptr;
    decltype(&BASS_RecordFree) BASS_RecordFree_ptr;
    decltype(&BASS_RecordStart) BASS_RecordStart_ptr;
    decltype(&BASS_ChannelStop) BASS_ChannelStop_ptr;
    decltype(&BASS_ChannelSetDSP) BASS_ChannelSetDSP_ptr;
    decltype(&BASS_ChannelRemoveDSP) BASS_ChannelRemoveDSP_ptr;

    // Helper functions
    bool LoadBASSLibraries();
    void UnloadBASSLibraries();
    bool InitializeBASS();
    bool InitializeMIDI();
    void CleanupBASS();
    void CleanupMIDI();

    // Error handling
    std::string GetLastBASSError() const;
    void LogBASSError(const std::string& operation) const;
};
