#pragma once

#include <string>
#include <vector>
#include <functional>
#include <thread>
#include <atomic>
#include <memory>

#ifdef _WIN32
#include <windows.h>
#include <mmeapi.h>
#endif

// Forward declaration - MIDIMessage is defined in alsa_midi.h
struct MIDIMessage;

// MIDI device information for Windows
struct WinMMMIDIDevice {
    int device_id;
    std::string name;
    std::string manufacturer;
    bool is_input;
    bool is_output;
};

// Callback function type for Windows MIDI input
using WinMMMIDIInputCallback = std::function<void(const MIDIMessage& message)>;

class WinMMMIDIInput {
public:
    WinMMMIDIInput();
    ~WinMMMIDIInput();

    // Initialize the MIDI input system
    bool Initialize();
    
    // Cleanup and release resources
    void Cleanup();
    
    // Check if the system is initialized
    bool IsInitialized() const;
    
    // Get list of available MIDI input devices
    std::vector<WinMMMIDIDevice> GetInputDevices() const;
    
    // Open a specific MIDI input device
    bool OpenDevice(int device_id);
    
    // Close the currently open device
    void CloseDevice();
    
    // Check if a device is currently open
    bool IsDeviceOpen() const;
    
    // Get information about the currently open device
    WinMMMIDIDevice GetCurrentDevice() const;
    
    // Start MIDI input processing
    bool StartInput();
    
    // Stop MIDI input processing
    void StopInput();
    
    // Check if input is currently active
    bool IsInputActive() const;
    
    // Set callback function for MIDI messages
    void SetMIDICallback(WinMMMIDIInputCallback callback);

private:
#ifdef _WIN32
    HMIDIIN midi_in_handle_;
    WinMMMIDIDevice current_device_;
    WinMMMIDIInputCallback midi_callback_;

    std::atomic<bool> input_active_;
    std::atomic<bool> device_open_;

    // Static callback function for Windows MIDI API
    static void CALLBACK MidiInProc(HMIDIIN hMidiIn, UINT wMsg, DWORD_PTR dwInstance, DWORD_PTR dwParam1, DWORD_PTR dwParam2);

    // Instance method to handle MIDI messages
    void HandleMidiMessage(UINT wMsg, DWORD_PTR dwParam1, DWORD_PTR dwParam2);

    // Convert Windows MIDI message to our MIDIMessage structure
    MIDIMessage ConvertToMIDIMessage(DWORD midiMessage, DWORD timestamp);

    // Get current timestamp in seconds
    double GetCurrentTimestamp() const;
#else
    // Dummy members for non-Windows platforms
    std::atomic<bool> input_active_;
    std::atomic<bool> device_open_;
    WinMMMIDIDevice current_device_;
    WinMMMIDIInputCallback midi_callback_;
#endif

    bool initialized_;
};
