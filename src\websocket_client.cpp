// Windows header order fix - must be before any other Windows headers
#if defined(_WIN32)
#ifndef NOMINMAX
#define NOMINMAX
#endif
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>
#endif

#include "websocket_client.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <cstring>
#include <zlib.h>
#include <cmath>
#include <algorithm>

// Static protocol definition
const char* WebSocketClient::protocolName_ = "piano-protocol";

struct lws_protocols WebSocketClient::protocols_[] = {
    {
        WebSocketClient::protocolName_,
        WebSocketClient::lwsCallback,
        0,
        65536, // Increased from 4096 to 64KB for large user lists
        0, nullptr, 0
    },
    { nullptr, nullptr, 0, 0, 0, nullptr, 0 } // terminator
};

WebSocketClient::WebSocketClient()
    : connected_(false)
    , shouldStop_(false)
    , disconnectHandled_(false)
    , context_(nullptr)
    , wsi_(nullptr)
    , lastSendTime_(std::chrono::steady_clock::now())
    , autoReconnect_(false)
    , lastPort_(0)
    , reconnectAttempts_(0)
    , lastMidiBatchTime_(std::chrono::steady_clock::now())
{
    ignoreTLSVerify_ = false; // Initialize ignoreTLSVerify_ to false
}

WebSocketClient::~WebSocketClient() {
    std::cerr << "WebSocketClient destructor called" << std::endl;

    // Check if we need to do any cleanup
    bool needsCleanup = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        needsCleanup = (context_ != nullptr || wsi_ != nullptr || connected_ || !shouldStop_);

        // Force stop all operations
        shouldStop_ = true;
        autoReconnect_ = false;
        connected_ = false;
    }

    // Wake up all waiting threads
    messageCondition_.notify_all();

    // Only call disconnect if we actually need cleanup
    if (needsCleanup) {
        std::cerr << "Cleanup needed, calling disconnect..." << std::endl;
        disconnect();
    } else {
        std::cerr << "No cleanup needed, skipping disconnect" << std::endl;
    }

    // Stop MIDI batch thread
    stopMidiBatchThread();

    // Wait for reconnect thread to finish with timeout
    if (reconnectThread_ && reconnectThread_->joinable()) {
        std::cerr << "Waiting for reconnect thread to finish..." << std::endl;

        // Simple timeout mechanism without std::async to avoid memory issues
        bool joined = false;
        std::thread timeoutThread([this, &joined]() {
            reconnectThread_->join();
            joined = true;
        });

        // Wait for up to 2 seconds
        for (int i = 0; i < 20 && !joined; ++i) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        if (joined) {
            timeoutThread.join();
        } else {
            std::cerr << "Reconnect thread join timed out, detaching..." << std::endl;
            reconnectThread_->detach();
            timeoutThread.detach();
        }
        reconnectThread_.reset();
    }

    std::cerr << "WebSocketClient destructor completed" << std::endl;
}

bool WebSocketClient::connect(const std::string& host, int port) {
    std::cerr << "WebSocketClient::connect() called with host=" << host << ", port=" << port << std::endl;

    // Update GUI status for initial connection attempt
    if (errorCallback_) {
        errorCallback_("Connecting to " + host + ":" + std::to_string(port) + "...");
    }

    // Store connection info for auto-reconnect
    lastHost_ = host;
    lastPort_ = port;
    reconnectAttempts_ = 0;

    // Always ensure clean state before connecting
    std::cerr << "Ensuring clean state before connecting..." << std::endl;

    // Set stop flag to ensure any existing operations stop
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        shouldStop_ = true;
        connected_ = false;
    }

    // Wake up any waiting threads
    messageCondition_.notify_all();

    // Clean up any existing resources with timeout
    if (serviceThread_) {
        if (serviceThread_->joinable()) {
            std::cerr << "Cleaning up existing service thread..." << std::endl;

            // Simple timeout mechanism without std::async
            bool joined = false;
            std::thread timeoutThread([this, &joined]() {
                serviceThread_->join();
                joined = true;
            });

            // Wait for up to 1 second
            for (int i = 0; i < 10 && !joined; ++i) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }

            if (joined) {
                timeoutThread.join();
            } else {
                std::cerr << "Service thread cleanup timed out, detaching..." << std::endl;
                serviceThread_->detach();
                timeoutThread.detach();
            }
        }
        serviceThread_.reset();
    }

    if (messageProcessThread_) {
        if (messageProcessThread_->joinable()) {
            std::cerr << "Cleaning up existing message thread..." << std::endl;

            // Simple timeout mechanism without std::async
            bool joined = false;
            std::thread timeoutThread([this, &joined]() {
                messageProcessThread_->join();
                joined = true;
            });

            // Wait for up to 1 second
            for (int i = 0; i < 10 && !joined; ++i) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }

            if (joined) {
                timeoutThread.join();
            } else {
                std::cerr << "Message thread cleanup timed out, detaching..." << std::endl;
                messageProcessThread_->detach();
                timeoutThread.detach();
            }
        }
        messageProcessThread_.reset();
    }

    if (context_) {
        std::cerr << "Cleaning up existing context..." << std::endl;
        lws_context_destroy(context_);
        context_ = nullptr;
    }

    wsi_ = nullptr;
    std::cerr << "Clean state achieved" << std::endl;

    // Reset flags for new connection
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        shouldStop_ = false;
        disconnectHandled_ = false;
        std::cerr << "Reset shouldStop_ and disconnectHandled_ flags to false" << std::endl;
    }


    
    // Create LWS context
    struct lws_context_creation_info info;
    memset(&info, 0, sizeof(info));

    info.port = CONTEXT_PORT_NO_LISTEN;
    info.protocols = protocols_;
    info.gid = -1;
    info.uid = -1;
    info.user = this;

    // Optimize for large user groups
    info.timeout_secs = 1;  // Minimum is 1 second for libwebsockets
    info.max_http_header_data = 8192;  // Increased header buffer
    info.max_http_header_pool = 16;    // More header pools
    info.keepalive_timeout = 60;       // Keep connections alive longer
    // Ensure OpenSSL global init when using SSL (required on some platforms)
    if (useSSL_) {
        info.options |= LWS_SERVER_OPTION_DO_SSL_GLOBAL_INIT;
    }
    
    context_ = lws_create_context(&info);
    if (!context_) {
        return false;
    }
    
    // Create connection
    struct lws_client_connect_info ccinfo;
    memset(&ccinfo, 0, sizeof(ccinfo));

    ccinfo.context = context_;
    // Keep strings alive during handshake
    connectAddress_ = host;
    ccinfo.address = connectAddress_.c_str();
    ccinfo.port = port;
    // Use configured path
    ccinfo.path = wsPath_.empty() ? "/" : wsPath_.c_str();
    // Build Host header (include port if non-default for scheme)
    if ((useSSL_ && port != 443) || (!useSSL_ && port != 80)) {
        connectHostHeader_ = host + ":" + std::to_string(port);
    } else {
        connectHostHeader_ = host;
    }
    ccinfo.host = connectHostHeader_.c_str();
    // Origin matching scheme
    originHeader_ = std::string(useSSL_ ? "https://" : "http://") + connectHostHeader_;
    ccinfo.origin = originHeader_.c_str();
    // Subprotocol: allow empty to disable
    ccinfo.protocol = subprotocol_.empty() ? nullptr : subprotocol_.c_str();
    // SSL if requested
    if (useSSL_) {
        ccinfo.ssl_connection = LCCSCF_USE_SSL;
        if (ignoreTLSVerify_) {
            ccinfo.ssl_connection |= LCCSCF_ALLOW_SELFSIGNED;
            ccinfo.ssl_connection |= LCCSCF_SKIP_SERVER_CERT_HOSTNAME_CHECK;
            ccinfo.ssl_connection |= LCCSCF_ALLOW_EXPIRED;
        }
    }
    // Some servers require explicit ALPN for ws over TLS
    if (useSSL_) {
        ccinfo.alpn = "http/1.1";
    }
    ccinfo.userdata = this;

    // Set connection timeout to 2 seconds (in milliseconds)
    ccinfo.retry_and_idle_policy = nullptr;  // Use default retry policy
    
    // Enable basic MPP mode if path looks like MPP servers or a desired room is set
    // Common MPP endpoints: /, /mpp, /mppclient, /multiplayer, /socket, /v3
    {
        std::string p = wsPath_.empty() ? "/" : wsPath_;
        // Engine.IO detection: presence of "EIO=" in query or path contains "/socket.io/"
        engineIoMode_ = (p.find("EIO=") != std::string::npos) || (p.find("/socket.io/") != std::string::npos);
        if (engineIoMode_) {
            mppMode_ = true; // still aim to speak MPP inside Socket.IO layer
        } else {
            if (!mppDesiredRoom_.empty() || p == "/" || p == "/mpp" || p == "/socket" || p == "/mppclient" || p == "/v3") {
                mppMode_ = true;
            } else {
                mppMode_ = false;
            }
        }
    }

    wsi_ = lws_client_connect_via_info(&ccinfo);
    if (!wsi_) {
        lws_context_destroy(context_);
        context_ = nullptr;

        // Update GUI status for connection failure
        if (errorCallback_) {
            errorCallback_("Failed to connect to " + host + ":" + std::to_string(port));
        }

        return false;
    }

    // Start WebSocket service thread (handles network I/O only)
    std::cerr << "Creating service thread..." << std::endl;
    serviceThread_ = std::make_unique<std::thread>([this]() {
        std::cerr << "Service thread started" << std::endl;
        while (!shouldStop_) {
            // Process WebSocket events with very short timeout for maximum responsiveness
            int result = lws_service(context_, 10); // 10ms timeout for very quick response

            // If service returns non-zero, there might be an error
            if (result < 0) {
                std::cerr << "lws_service returned error: " << result << std::endl;
                break;
            }

            // Small sleep to prevent excessive CPU usage while staying very responsive
            std::this_thread::sleep_for(std::chrono::milliseconds(1));

            // Engine.IO keepalive ping
            if (engineIoMode_ && connected_) {
                if (engineIoPingIntervalMs_ > 0) {
                    auto now = std::chrono::steady_clock::now();
                    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastEngineIoPing_).count();
                    // send ping a bit before timeout
                    if (elapsed >= std::max(1000, engineIoPingIntervalMs_ - 500)) {
                        queueRaw("2"); // Engine.IO ping
                        lastEngineIoPing_ = now;
                        if (context_) lws_cancel_service(context_);
                    }
                }
            }
        }
        std::cerr << "Service thread ending" << std::endl;
    });

    // Start MIDI batch thread
    startMidiBatchThread();

    // Start message processing thread (handles incoming message processing)
    std::cerr << "Creating message processing thread..." << std::endl;
    messageProcessThread_ = std::make_unique<std::thread>([this]() {
        std::cerr << "Message processing thread started" << std::endl;
        while (!shouldStop_) {
            std::unique_lock<std::mutex> lock(messageMutex_);

            // Wait for messages or stop signal
            messageCondition_.wait(lock, [this] {
                return !incomingMessages_.empty() || shouldStop_;
            });

            // Process all available messages
            while (!incomingMessages_.empty() && !shouldStop_) {
                std::string message = incomingMessages_.front();
                incomingMessages_.pop();

                // Release lock while processing message
                lock.unlock();
                handleMessage(message);
                lock.lock();
            }
        }
        std::cerr << "Message processing thread ending" << std::endl;
    });

    std::cerr << "WebSocketClient::connect() completed successfully" << std::endl;
    return true;
}

void WebSocketClient::disconnect() {
    std::cerr << "WebSocketClient::disconnect() called" << std::endl;

    // Check if already disconnected or never connected
    bool alreadyDisconnected = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        alreadyDisconnected = shouldStop_ && !connected_ && !context_ && !wsi_;

        // If we never connected (no context was created), just return
        if (!context_ && !wsi_ && !connected_) {
            std::cerr << "Never connected, skipping disconnect" << std::endl;
            shouldStop_ = true;
            autoReconnect_ = false;
            return;
        }

        // Set stop flag first to prevent new operations
        shouldStop_ = true;
        connected_ = false;
        // Disable auto-reconnect for manual disconnection
        autoReconnect_ = false;
        std::cerr << "Set shouldStop_ = true, connected_ = false, autoReconnect_ = false" << std::endl;
    }

    if (alreadyDisconnected) {
        std::cerr << "Already disconnected, skipping cleanup" << std::endl;
        return;
    }

    // Update GUI status for manual disconnection
    if (errorCallback_) {
        errorCallback_("Disconnecting...");
    }

    // Stop MIDI batch thread and flush pending data
    stopMidiBatchThread();

    // Wake up message processing thread first
    std::cerr << "Notifying message processing thread..." << std::endl;
    messageCondition_.notify_all();

    // Close WebSocket connection properly (only if still valid)
    bool hasValidConnection = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        hasValidConnection = (wsi_ != nullptr && context_ != nullptr);
        if (hasValidConnection) {
            std::cerr << "Requesting connection close..." << std::endl;
            // Request connection close
            lws_close_reason(wsi_, LWS_CLOSE_STATUS_NORMAL, nullptr, 0);
            // Don't set wsi_ to nullptr here - let the callback handle it
        } else {
            std::cerr << "Connection already closed, skipping close request" << std::endl;
            wsi_ = nullptr; // Ensure it's null
        }
    }

    // Wake up service thread to process the close (only if context is valid)
    if (hasValidConnection && context_) {
        std::cerr << "Cancelling service..." << std::endl;
        lws_cancel_service(context_);

        // Give the service thread a moment to process the close
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // Perform cleanup
    cleanupAfterDisconnect(true); // true = set shouldStop_

    // Update GUI status for completed disconnection
    if (errorCallback_) {
        errorCallback_("Disconnected");
    }

    std::cerr << "WebSocketClient::disconnect() completed" << std::endl;
}

bool WebSocketClient::isConnected() const {
    return connected_;
}

bool WebSocketClient::sendMidi(const std::vector<MidiBuffer>& buffers) {
    if (!connected_ || buffers.empty()) {
        return false;
    }

    // If MPP mode, translate to MPP note message: { m: "n", n: [ { n, v } ... ] }
    if (mppMode_) {
        nlohmann::json msg;
        msg["m"] = "n";
        // Base server time for this batch (server ms ~= local now + offset)
        double nowLocalMs = (double)std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        long long serverNowMs = (long long)(nowLocalMs + serverTimeOffsetMs_);
        msg["t"] = serverNowMs;
        nlohmann::json arr = nlohmann::json::array();
        for (const auto& b : buffers) {
            nlohmann::json ev;
            ev["n"] = static_cast<int>(b.note);
            // NoteOff represented as v:0
            ev["v"] = (b.status == 128) ? 0 : static_cast<int>(b.velocity);
            // per-event delta time in ms (Client.js uses 'd')
            ev["d"] = static_cast<int>(b.getDeltaTime());
            arr.push_back(ev);
        }
        msg["n"] = std::move(arr);
        queueMessage(msg);
        return true;
    }

    // Limit the number of buffers per message to prevent large messages
    // Increased to 200 for better handling of large user groups
    const size_t maxBuffersPerMessage = 200;

    // Split large buffer arrays into smaller chunks
    for (size_t i = 0; i < buffers.size(); i += maxBuffersPerMessage) {
        size_t end = std::min(i + maxBuffersPerMessage, buffers.size());
        std::vector<MidiBuffer> chunk(buffers.begin() + i, buffers.begin() + end);

        nlohmann::json message;
        message["type"] = "midi";

        // Convert MidiBuffer structs to binary data for compression
        std::vector<uint8_t> binaryData;
        binaryData.reserve(chunk.size() * 8); // Each MidiBuffer is 8 bytes

        for (const auto& buffer : chunk) {
            binaryData.push_back(buffer.status);
            binaryData.push_back(buffer.note);
            binaryData.push_back(buffer.velocity);
            binaryData.push_back(buffer.color_r);
            binaryData.push_back(buffer.color_g);
            binaryData.push_back(buffer.color_b);
            binaryData.push_back(buffer.delta_hi);
            binaryData.push_back(buffer.delta_lo);
        }

        // Compress the binary data
        std::vector<uint8_t> compressedData = compressData(binaryData);

        // Debug output
        std::cerr << "Original MIDI data size: " << binaryData.size() << " bytes" << std::endl;
        std::cerr << "Compressed data size: " << compressedData.size() << " bytes" << std::endl;

        if (compressedData.empty()) {
            // Fallback to uncompressed if compression fails
            nlohmann::json buffersArray = nlohmann::json::array();
            for (const auto& buffer : chunk) {
                nlohmann::json bufferArray = nlohmann::json::array();
                bufferArray.push_back(buffer.status);
                bufferArray.push_back(buffer.note);
                bufferArray.push_back(buffer.velocity);
                bufferArray.push_back(buffer.color_r);
                bufferArray.push_back(buffer.color_g);
                bufferArray.push_back(buffer.color_b);
                bufferArray.push_back(buffer.delta_hi);
                bufferArray.push_back(buffer.delta_lo);
                buffersArray.push_back(bufferArray);
            }
            message["buffers"] = buffersArray;
            message["compressed"] = false;
        } else {
            // Send compressed data as hex string (more reliable than base64)
            std::string hexData;
            hexData.reserve(compressedData.size() * 2);
            const char* hexChars = "0123456789abcdef";

            for (uint8_t byte : compressedData) {
                hexData += hexChars[(byte >> 4) & 0x0F];
                hexData += hexChars[byte & 0x0F];
            }

            message["compressed_data"] = hexData;
            message["compressed"] = true;
            message["original_size"] = binaryData.size();
            message["buffer_count"] = chunk.size();
        }

        // Queue each chunk separately
        queueMessage(message);
    }

    return true;
}

bool WebSocketClient::sendMidi(const MidiBuffer& buffer) {
    return sendMidi(std::vector<MidiBuffer>{buffer});
}

bool WebSocketClient::sendNoteOn(uint8_t note, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    MidiBuffer buffer;
    buffer.status = 144; // Note On
    buffer.note = note;
    buffer.velocity = velocity;
    buffer.setColor(color);
    buffer.setDeltaTime(deltaTime);

    return sendMidiBatched(buffer);
}

bool WebSocketClient::sendNoteOff(uint8_t note, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    MidiBuffer buffer;
    buffer.status = 128; // Note Off
    buffer.note = note;
    buffer.velocity = velocity;
    buffer.setColor(color);
    buffer.setDeltaTime(deltaTime);

    return sendMidiBatched(buffer);
}

bool WebSocketClient::sendMidiBatched(const MidiBuffer& buffer) {
    if (!connected_) {
        return false;
    }

    std::lock_guard<std::mutex> lock(midiBatchMutex_);

    // Add buffer to pending batch
    pendingMidiBuffers_.push_back(buffer);

    // If batch is full, process it immediately
    if (pendingMidiBuffers_.size() >= maxMidiBatchSize_) {
        processPendingMidiBatch();
    }

    return true;
}

void WebSocketClient::flushMidiBatch() {
    std::lock_guard<std::mutex> lock(midiBatchMutex_);
    if (!pendingMidiBuffers_.empty()) {
        processPendingMidiBatch();
    }
}

bool WebSocketClient::updateUsername(const std::string& username) {
    if (!connected_) {
        // Remember for when we connect
        desiredUsername_ = username;
        return false;
    }

    nlohmann::json message;
    if (mppMode_) {
        message["m"] = "userset";
        nlohmann::json setObj;
        setObj["name"] = username;
        message["set"] = setObj;
    } else {
        message["type"] = "set_username";
        message["username"] = username;
    }

    // Update local user info
    currentUser_.username = username;
    desiredUsername_ = username;

    return sendMessage(message);
}

bool WebSocketClient::requestUsers() {
    if (!connected_) {
        return false;
    }
    
    nlohmann::json message;
    message["type"] = "get_users";
    
    return sendMessage(message);
}

bool WebSocketClient::sendPing() {
    if (!connected_) {
        return false;
    }

    if (mppMode_) {
        nlohmann::json tmsg;
        tmsg["m"] = "t";
        return sendMessage(tmsg);
    } else {
        nlohmann::json message;
        message["type"] = "ping";
        return sendMessage(message);
    }
}

bool WebSocketClient::sendChat(const std::string& message) {
    if (!connected_) {
        return false;
    }

    nlohmann::json chatMessage;
    if (mppMode_) {
        chatMessage["m"] = "a";
        chatMessage["message"] = message;
    } else {
        chatMessage["type"] = "chat";
        chatMessage["message"] = message;
    }

    return sendMessage(chatMessage);
}

void WebSocketClient::maybeSendUserset() {
    if (!mppMode_) return;
    if (desiredUsername_.empty()) return;
    nlohmann::json msg; msg["m"] = "userset"; nlohmann::json set; set["name"] = desiredUsername_; msg["set"] = set; queueMessage(msg);
}

// Callback setters
void WebSocketClient::setConnectedCallback(ConnectedCallback callback) {
    connectedCallback_ = callback;
}

void WebSocketClient::setMidiCallback(MidiCallback callback) {
    midiCallback_ = callback;
}

void WebSocketClient::setUsernameUpdatedCallback(UsernameUpdatedCallback callback) {
    usernameUpdatedCallback_ = callback;
}

void WebSocketClient::setUserUpdateCallback(UserUpdateCallback callback) {
    userUpdateCallback_ = callback;
}

void WebSocketClient::setErrorCallback(ErrorCallback callback) {
    errorCallback_ = callback;
}

void WebSocketClient::setPongCallback(PongCallback callback) {
    pongCallback_ = callback;
}

void WebSocketClient::setChatCallback(ChatCallback callback) {
    chatCallback_ = callback;
}

void WebSocketClient::setUseSSL(bool enable) {
    useSSL_ = enable;
}

void WebSocketClient::setWebSocketPath(const std::string& path) {
    wsPath_ = path.empty() ? "/" : path;
}

void WebSocketClient::setSubprotocol(const std::string& subprotocol) {
    subprotocol_ = subprotocol;
}

void WebSocketClient::setIgnoreTLSVerify(bool enable) {
    ignoreTLSVerify_ = enable;
}

void WebSocketClient::setMPPDesiredRoom(const std::string& room) {
    mppDesiredRoom_ = room;
    // Activating MPP mode when a room is specified
    mppMode_ = true;
}

void WebSocketClient::setMPPChannelSettings(const nlohmann::json& settings) {
    mppChannelSettings_ = settings;
}

void WebSocketClient::joinMPPChannelNow() {
    if (!mppMode_) return;
    std::string room = mppDesiredRoom_.empty() ? std::string("lobby") : mppDesiredRoom_;
    nlohmann::json chMsg;
    chMsg["m"] = "ch";
    chMsg["_id"] = room;
    if (!mppChannelSettings_.is_null() && !mppChannelSettings_.empty()) {
        chMsg["set"] = mppChannelSettings_;
    }
    queueMessage(chMsg);
}

bool WebSocketClient::sendMessage(const nlohmann::json& message) {
    if (engineIoMode_) {
        // Socket.IO event frame: 42 + <json>
        std::string payload = message.dump();
        queueRaw(std::string("42") + payload);
        return true;
    } else {
        // Queue the message for thread-safe sending
        queueMessage(message);
        return true;
    }
}

void WebSocketClient::queueMessage(const nlohmann::json& message) {
    std::lock_guard<std::mutex> lock(outgoingMutex_);

    // Limit queue size to prevent memory issues
    // Increased to 20000 for handling large numbers of users (100+ users)
    const size_t maxQueueSize = 20000;
    if (outgoingMessages_.size() >= maxQueueSize) {
        // Drop oldest messages if queue is full
        while (outgoingMessages_.size() >= maxQueueSize) {
            outgoingMessages_.pop();
        }
    }

    // Build payload according to protocol
    if (engineIoMode_ && mppMode_) {
        // Convert { m:"evt", ...rest } to Socket.IO event: 42["evt", { ...rest }]
        if (message.contains("m") && message["m"].is_string()) {
            std::string evt = message["m"].get<std::string>();
            nlohmann::json data = message;
            data.erase("m");
            nlohmann::json sio = nlohmann::json::array();
            sio.push_back(evt);
            sio.push_back(data);
            outgoingMessages_.push(std::string("42") + sio.dump());
        } else {
            // Fallback: wrap as a generic event name 'msg'
            nlohmann::json sio = nlohmann::json::array();
            sio.push_back("msg");
            sio.push_back(message);
            outgoingMessages_.push(std::string("42") + sio.dump());
        }
    } else {
        std::string payload;
        if (mppMode_) {
            // Raw MPP expects an array-of-messages per frame
            payload = "[" + message.dump() + "]";
        } else {
            payload = message.dump();
        }
        outgoingMessages_.push(std::move(payload));
    }

    // Request writable callback to process the queue
    if (connected_ && wsi_) {
        lws_callback_on_writable(wsi_);
        // Don't call lws_cancel_service here as it causes excessive callbacks
        // The service thread will pick up the writable callback naturally
    }
}

void WebSocketClient::queueRaw(const std::string& text) {
    std::lock_guard<std::mutex> lock(outgoingMutex_);
    const size_t maxQueueSize = 20000;
    if (outgoingMessages_.size() >= maxQueueSize) {
        while (outgoingMessages_.size() >= maxQueueSize) outgoingMessages_.pop();
    }
    outgoingMessages_.push(text);
    if (connected_ && wsi_) {
        lws_callback_on_writable(wsi_);
    }
}

bool WebSocketClient::sendMessageDirect(const std::string& messageStr) {
    if (!connected_ || !wsi_) {
        return false;
    }

    size_t len = messageStr.length();

    // Check if message is too large
    if (len > 262144) { // 256KB limit (increased from 32KB for large user lists)
        // Split large messages or reject them
        return false;
    }

    // Allocate buffer with LWS_PRE padding
    std::vector<unsigned char> buffer(LWS_PRE + len);
    memcpy(&buffer[LWS_PRE], messageStr.c_str(), len);

    int result = lws_write(wsi_, &buffer[LWS_PRE], len, LWS_WRITE_TEXT);

    return result >= 0;
}



void WebSocketClient::processOutgoingQueue() {
    std::lock_guard<std::mutex> lock(outgoingMutex_);

    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastSend = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastSendTime_);

    // Rate limiting to prevent excessive sending (reduced for large user groups)
    if (timeSinceLastSend.count() < 0.5) { // 0.5ms minimum between sends
        return;
    }

    // Process messages in small batches
    int processed = 0;
    const int maxPerCall = 3; // Smaller batch size to reduce callback frequency

    while (!outgoingMessages_.empty() && processed < maxPerCall) {
        std::string message = outgoingMessages_.front();
        outgoingMessages_.pop();

        // Release lock temporarily to send message
        outgoingMutex_.unlock();
        bool success = sendMessageDirect(message);
        outgoingMutex_.lock();

        if (success) {
            lastSendTime_ = std::chrono::steady_clock::now();
            processed++;
        } else {
            // If send failed, put message back at front of queue
            std::queue<std::string> temp;
            temp.push(message);
            while (!outgoingMessages_.empty()) {
                temp.push(outgoingMessages_.front());
                outgoingMessages_.pop();
            }
            outgoingMessages_ = temp;
            break;
        }
    }
}

void WebSocketClient::handleMessage(const std::string& message) {
    // Check if we should stop processing (client is being destroyed)
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        if (shouldStop_) {
            return;
        }
    }

    try {
        // If Engine.IO, strip frame prefix
        std::string payload = message;
        if (engineIoMode_) {
            // Engine.IO text frames begin with one of: '0' open, '4' message, '3' ping, '2' ping from client, etc.
            if (!payload.empty()) {
                char code = payload[0];
                if (code == '0') {
                    // Open: payload after '0' is JSON with pingInterval, etc.
                    socketIoConnected_ = false;
                    try {
                        nlohmann::json open = nlohmann::json::parse(payload.substr(1));
                        if (open.contains("pingInterval")) {
                            engineIoPingIntervalMs_ = open["pingInterval"].get<int>();
                            lastEngineIoPing_ = std::chrono::steady_clock::now();
                        }
                    } catch (...) {
                        // ignore
                    }
                    return;
                } else if (code == '3') {
                    // PONG from server
                    return;
                } else if (code == '4') {
                    // Message layer (Socket.IO). Next char may be '0' (connect ack) or '2' (event)
                    if (payload.size() >= 2) {
                        char sicode = payload[1];
                        if (sicode == '0') {
                            // 40 => connected
                            socketIoConnected_ = true;
                            // After 40, send hi/ch now
                            if (mppMode_) {
                                nlohmann::json hiMsg; hiMsg["m"] = "hi"; queueMessage(hiMsg);
                                joinMPPChannelNow();
                                maybeSendUserset();
                            }
                            return;
                        } else if (sicode == '2') {
                            // 42<json> => event JSON payload follows from index 2
                            std::string raw = payload.substr(2);
                            try {
                                nlohmann::json evt = nlohmann::json::parse(raw);
                                if (evt.is_array() && evt.size() >= 1) {
                                    std::string name = evt[0].is_string() ? evt[0].get<std::string>() : "";
                                    nlohmann::json data = (evt.size() >= 2) ? evt[1] : nlohmann::json::object();
                                    // Rewrap to MPP-style object for unified handling
                                    if (!name.empty()) {
                                        nlohmann::json mpp;
                                        mpp["m"] = name;
                                        if (data.is_object()) {
                                            // merge keys into mpp
                                            for (auto it = data.begin(); it != data.end(); ++it) {
                                                mpp[it.key()] = it.value();
                                            }
                                        } else if (!data.is_null()) {
                                            mpp["data"] = data;
                                        }
                                        // Pass single object to handler below
                                        payload = mpp.dump();
                                    } else {
                                        payload = data.dump();
                                    }
                                } else {
                                    payload = raw;
                                }
                            } catch (...) {
                                payload = raw;
                            }
                        } else {
                            // Unknown, drop
                            return;
                        }
                    } else {
                        return;
                    }
                } else {
                    // Other codes not handled
                    return;
                }
            }
        }

        // MPP servers send an array of message objects per frame
        nlohmann::json parsed = nlohmann::json::parse(payload);
    if (parsed.is_array()) {
            for (const auto& data : parsed) {
                if (!data.is_object()) continue;
                // Recurse-like: duplicate handling logic in a local lambda to reuse existing code paths
                // For simplicity, inline minimal handling for MPP here
                if (mppMode_ && data.contains("m") && data["m"].is_string()) {
                    std::string mt = data["m"].get<std::string>();
                    if (mt == "hi") {
                        if (data.contains("u")) {
                            const auto& u = data["u"]; clientId_ = u.value("_id", ""); currentUser_.id = clientId_; currentUser_.username = u.value("name", "Player"); connected_ = true; if (connectedCallback_ && !shouldStop_) connectedCallback_(clientId_, currentUser_, activeUsers_);
                        }
                        continue;
                    }
                    if (mt == "ch") {
                        activeUsers_.clear(); const auto& ch = data["ch"]; if (ch.contains("ppl")) { for (const auto& p : ch["ppl"]) { UserInfo ui; ui.id = p.value("id", p.value("_id", "")); ui.username = p.value("name", ""); ui.connectedClients = 1; activeUsers_.push_back(ui);} }
                        if (userUpdateCallback_ && !shouldStop_) userUpdateCallback_(activeUsers_);
                        continue;
                    }
                    if (mt == "p") {
                        // participant add/update
                        std::string id = data.value("id", "");
                        std::string name = data.value("name", "");
                        if (!id.empty()) {
                            bool found = false;
                            for (auto &u : activeUsers_) {
                                if (u.id == id) { if (!name.empty()) u.username = name; found = true; break; }
                            }
                            if (!found) { UserInfo ui{ id, name, 1 }; activeUsers_.push_back(ui); }
                            if (userUpdateCallback_ && !shouldStop_) userUpdateCallback_(activeUsers_);
                        }
                        continue;
                    }
                    if (mt == "m") {
                        // participant metadata update (e.g., name change)
                        std::string id = data.value("id", "");
                        if (!id.empty()) {
                            for (auto &u : activeUsers_) {
                                if (u.id == id) {
                                    if (data.contains("name")) u.username = data.value("name", u.username);
                                    break;
                                }
                            }
                            if (userUpdateCallback_ && !shouldStop_) userUpdateCallback_(activeUsers_);
                        }
                        continue;
                    }
                    if (mt == "bye") {
                        // participant left; field is 'p' in Client.js
                        std::string id = data.value("p", data.value("id", ""));
                        if (!id.empty()) {
                            activeUsers_.erase(std::remove_if(activeUsers_.begin(), activeUsers_.end(), [&](const UserInfo& u){ return u.id == id; }), activeUsers_.end());
                            if (userUpdateCallback_ && !shouldStop_) userUpdateCallback_(activeUsers_);
                        }
                        continue;
                    }
                    if (mt == "t") {
                        // Time sync message: { m:"t", t: server_now_ms }
                        if (data.contains("t")) {
                            double now = (double)std::chrono::duration_cast<std::chrono::milliseconds>(
                                std::chrono::system_clock::now().time_since_epoch()).count();
                            double target = 0.0;
                            try { target = (double)data["t"].get<long long>() - now; } catch (...) {
                                try { target = data["t"].get<double>() - now; } catch (...) { target = serverTimeOffsetMs_; }
                            }
                            serverTimeOffsetMs_ = serverTimeOffsetMs_ + (target - serverTimeOffsetMs_) * 0.2; // ease
                        }
                        continue;
                    }
                    if (mt == "n") {
                            if (data.contains("n") && data["n"].is_array()) {
                                std::vector<MidiBuffer> buffers; for (const auto& ev : data["n"]) { if (ev.contains("n")) { int note = -1; if (ev["n"].is_number()) note = ev["n"].get<int>(); else if (ev["n"].is_string()) { try { note = std::stoi(ev["n"].get<std::string>()); } catch (...) { note = -1; } } if (note >= 0) { int vel = 0; if (ev.contains("v")) { if (ev["v"].is_number_float()) vel = std::clamp((int)std::round(ev["v"].get<double>() * 127.0), 0, 127); else if (ev["v"].is_number_integer()) vel = std::clamp(ev["v"].get<int>(), 0, 127); } bool stop = ev.contains("s") && ((ev["s"].is_number() && ev["s"].get<int>() != 0) || (ev["s"].is_boolean() && ev["s"].get<bool>())); MidiBuffer b{}; b.status = stop || vel == 0 ? 128 : 144; b.note = (uint8_t)note; b.velocity = (uint8_t)vel; b.setColor(0xFFFFFF); uint16_t delta = 0; if (ev.contains("d")) { try { delta = (uint16_t)std::clamp(ev["d"].get<int>(), 0, 65535); } catch (...) {} } else if (ev.contains("t")) { try { delta = (uint16_t)std::clamp(ev["t"].get<int>(), 0, 65535); } catch (...) {} } b.setDeltaTime(delta); buffers.push_back(b); } } }
                                UserInfo from{"","MPP",1}; if (!buffers.empty() && midiCallback_ && !shouldStop_) midiCallback_(buffers, from);
                            }
                            continue;
                        }
                    if (mt == "a") {
                            std::string message = data.value("message", ""); uint64_t ts = 0; if (data.contains("t")) { try { ts = data["t"].get<uint64_t>(); } catch (...) {} }
                            UserInfo from; if (data.contains("p")) { const auto& p = data["p"]; from.id = p.value("_id", ""); from.username = p.value("name", ""); }
                            if (chatCallback_ && !shouldStop_) chatCallback_(message, from, ts);
                            continue;
                        }
                    continue;
                }

                // Non-MPP path uses existing handler logic: emulate by setting a local copy and falling through
                nlohmann::json single = data;
                // From here down, reuse original single-object path
                if (!single.contains("type")) { continue; }
                std::string type = single["type"];
                if (type == "connected") {
                    if (single.contains("clientId") && single.contains("user")) {
                        clientId_ = single["clientId"]; auto userObj = single["user"]; currentUser_.id = userObj["id"]; currentUser_.username = userObj["username"]; activeUsers_.clear(); if (single.contains("activeUsers")) { for (const auto& userJson : single["activeUsers"]) { UserInfo user; user.id = userJson["id"]; user.username = userJson["username"]; user.connectedClients = userJson.value("connectedClients", 1); activeUsers_.push_back(user);} }
                        { std::lock_guard<std::mutex> lock(stateMutex_); if (connectedCallback_ && !shouldStop_) { connectedCallback_(clientId_, currentUser_, activeUsers_); } }
                    }
                } else if (type == "midi") {
                    // leave default path below (we're inside array iteration, so skip)
                } else if (type == "username_updated") {
                    if (single.contains("username")) { std::string newUsername = single["username"]; currentUser_.username = newUsername; if (usernameUpdatedCallback_ && !shouldStop_) { usernameUpdatedCallback_(newUsername); } }
                } else if (type == "user_update") {
                    if (single.contains("users")) { activeUsers_.clear(); for (const auto& userJson : single["users"]) { UserInfo user; user.id = userJson["id"]; user.username = userJson["username"]; user.connectedClients = userJson.value("connectedClients", 1); activeUsers_.push_back(user);} if (userUpdateCallback_ && !shouldStop_) { userUpdateCallback_(activeUsers_); } }
                } else if (type == "error") {
                    if (single.contains("message")) { std::string errorMessage = single["message"]; if (errorCallback_ && !shouldStop_) { errorCallback_(errorMessage); } }
                } else if (type == "pong") {
                    if (pongCallback_ && !shouldStop_) { pongCallback_(); }
                } else if (type == "chat") {
                    if (single.contains("message") && single.contains("fromUser") && single.contains("timestamp")) {
                        std::string message = single["message"]; uint64_t timestamp = single["timestamp"]; auto userObj = single["fromUser"]; UserInfo fromUser; fromUser.id = userObj["id"]; fromUser.username = userObj["username"]; if (chatCallback_ && !shouldStop_) { chatCallback_(message, fromUser, timestamp); }
                    }
                } else if (type == "chat_history") {
                    if (single.contains("messages")) { auto messages = single["messages"]; for (const auto& msgData : messages) { if (msgData.contains("message") && msgData.contains("fromUser") && msgData.contains("timestamp")) { std::string message = msgData["message"]; uint64_t timestamp = msgData["timestamp"]; auto userObj = msgData["fromUser"]; UserInfo fromUser; fromUser.id = userObj["id"]; fromUser.username = userObj["username"]; if (chatCallback_ && !shouldStop_) { chatCallback_(message, fromUser, timestamp); } } } }
                }
            }
            return; // handled whole array
        }
        nlohmann::json data = parsed;

        // MPP servers often use messages with m/t/a properties and different schema.
        // Detect and minimally translate to our internal events.
        if (mppMode_ && (!data.contains("type") && (data.contains("m") || data.contains("hi") || data.contains("ch")))) {
            // Basic MPP handling
            if (data.contains("hi")) {
                // Welcome payload contains u (user), motd, t (server time)
                const auto& hi = data["hi"];
                if (hi.contains("u")) {
                    const auto& u = hi["u"];
                    clientId_ = u.value("_id", "");
                    currentUser_.id = clientId_;
                    currentUser_.username = u.value("name", "Player");
                    connected_ = true;
                    if (connectedCallback_ && !shouldStop_) {
                        connectedCallback_(clientId_, currentUser_, activeUsers_);
                    }
                }
                return; // handled
            }

            if (data.contains("ch")) {
                // Channel state update. Build active users list from ch.ppl
                activeUsers_.clear();
                const auto& ch = data["ch"];
                if (ch.contains("ppl")) {
                    for (const auto& p : ch["ppl"]) {
                        UserInfo ui;
                        ui.id = p.value("_id", "");
                        ui.username = p.value("name", "");
                        ui.connectedClients = 1;
                        activeUsers_.push_back(ui);
                    }
                }
                if (userUpdateCallback_ && !shouldStop_) {
                    userUpdateCallback_(activeUsers_);
                }
                return; // handled
            }

            if (data.contains("m") && data["m"].is_string() && data["m"].get<std::string>() == "t") {
                if (data.contains("t")) {
                    double now = (double)std::chrono::duration_cast<std::chrono::milliseconds>(
                        std::chrono::system_clock::now().time_since_epoch()).count();
                    double target = 0.0;
                    try { target = (double)data["t"].get<long long>() - now; } catch (...) {
                        try { target = data["t"].get<double>() - now; } catch (...) { target = serverTimeOffsetMs_; }
                    }
                    serverTimeOffsetMs_ = serverTimeOffsetMs_ + (target - serverTimeOffsetMs_) * 0.2;
                }
                return; // handled
            }
            if (data.contains("m") && data["m"].is_string() && data["m"].get<std::string>() == "n") {
                // Note message: { m:"n", t: <server time>, n: [ { n: pitch, v: velocity, s: sustain?, t: time } ] }
                if (data.contains("n") && data["n"].is_array()) {
                    std::vector<MidiBuffer> buffers;
                    for (const auto& ev : data["n"]) {
                        if (ev.contains("n") && ev.contains("v")) {
                            int note = ev["n"].get<int>();
                            int vel = ev["v"].get<int>();
                            MidiBuffer b{};
                            b.status = vel > 0 ? 144 : 128;
                            b.note = static_cast<uint8_t>(note);
                            b.velocity = static_cast<uint8_t>(std::max(0, std::min(127, vel)));
                            b.setColor(0xFFFFFF);
                            b.setDeltaTime(0);
                            buffers.push_back(b);
                        }
                    }
                    UserInfo dummy{ "", "MPP", 1 };
                    if (!buffers.empty() && midiCallback_ && !shouldStop_) {
                        midiCallback_(buffers, dummy);
                    }
                }
                return; // handled
            }
            if (data.contains("m") && data["m"].is_string() && data["m"].get<std::string>() == "a") {
                // Chat message
                std::string text = data.value("message", "");
                UserInfo from;
                if (data.contains("p") && data["p"].is_object()) {
                    const auto& p = data["p"];
                    from.id = p.value("_id", "");
                    from.username = p.value("name", "");
                } else {
                    from.id = "";
                    from.username = "MPP";
                }
                uint64_t ts = 0;
                if (data.contains("t")) {
                    try { ts = data["t"].get<uint64_t>(); } catch (...) {}
                }
                if (chatCallback_ && !shouldStop_) {
                    chatCallback_(text, from, ts);
                }
                return; // handled
            }
            // Unhandled MPP message types are ignored
        }

        if (!data.contains("type")) {
            return;
        }

        std::string type = data["type"];

        if (type == "connected") {
            if (data.contains("clientId") && data.contains("user")) {
                clientId_ = data["clientId"];

                auto userObj = data["user"];
                currentUser_.id = userObj["id"];
                currentUser_.username = userObj["username"];

                // Parse active users
                activeUsers_.clear();
                if (data.contains("activeUsers")) {
                    for (const auto& userJson : data["activeUsers"]) {
                        UserInfo user;
                        user.id = userJson["id"];
                        user.username = userJson["username"];
                        user.connectedClients = userJson.value("connectedClients", 1);
                        activeUsers_.push_back(user);
                    }
                }

                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (connectedCallback_ && !shouldStop_) {
                        connectedCallback_(clientId_, currentUser_, activeUsers_);
                    }
                }
            }
        }
        else if (type == "midi") {
            if (data.contains("fromUser")) {
                std::vector<MidiBuffer> buffers;

                // Check if data is compressed
                if (data.contains("compressed") && data["compressed"].get<bool>()) {
                    // Handle compressed data
                    if (data.contains("compressed_data") && data.contains("buffer_count")) {
                        std::string hexData = data["compressed_data"];
                        size_t bufferCount = data["buffer_count"];

                        // Hex string decoding
                        std::vector<uint8_t> compressedData;
                        compressedData.reserve(hexData.length() / 2);

                        for (size_t i = 0; i < hexData.length(); i += 2) {
                            if (i + 1 < hexData.length()) {
                                char high = hexData[i];
                                char low = hexData[i + 1];

                                uint8_t highNibble = 0;
                                uint8_t lowNibble = 0;

                                // Convert hex characters to nibbles
                                if (high >= '0' && high <= '9') highNibble = high - '0';
                                else if (high >= 'a' && high <= 'f') highNibble = high - 'a' + 10;
                                else if (high >= 'A' && high <= 'F') highNibble = high - 'A' + 10;

                                if (low >= '0' && low <= '9') lowNibble = low - '0';
                                else if (low >= 'a' && low <= 'f') lowNibble = low - 'a' + 10;
                                else if (low >= 'A' && low <= 'F') lowNibble = low - 'A' + 10;

                                compressedData.push_back((highNibble << 4) | lowNibble);
                            }
                        }

                        // Decompress the data
                        std::cerr << "Received compressed data size: " << compressedData.size() << " bytes" << std::endl;
                        std::vector<uint8_t> decompressedData = decompressData(compressedData);
                        std::cerr << "Decompressed data size: " << decompressedData.size() << " bytes" << std::endl;

                        // Convert binary data back to MidiBuffer structs
                        if (decompressedData.size() >= bufferCount * 8) {
                            for (size_t i = 0; i < bufferCount; i++) {
                                size_t offset = i * 8;
                                MidiBuffer buffer;
                                buffer.status = decompressedData[offset];
                                buffer.note = decompressedData[offset + 1];
                                buffer.velocity = decompressedData[offset + 2];
                                buffer.color_r = decompressedData[offset + 3];
                                buffer.color_g = decompressedData[offset + 4];
                                buffer.color_b = decompressedData[offset + 5];
                                buffer.delta_hi = decompressedData[offset + 6];
                                buffer.delta_lo = decompressedData[offset + 7];
                                buffers.push_back(buffer);
                            }
                        }
                    }
                } else if (data.contains("buffers")) {
                    // Handle uncompressed data (legacy format)
                    for (const auto& bufferJson : data["buffers"]) {
                        if (bufferJson.size() >= 8) {
                            MidiBuffer buffer;
                            buffer.status = bufferJson[0];
                            buffer.note = bufferJson[1];
                            buffer.velocity = bufferJson[2];
                            buffer.color_r = bufferJson[3];
                            buffer.color_g = bufferJson[4];
                            buffer.color_b = bufferJson[5];
                            buffer.delta_hi = bufferJson[6];
                            buffer.delta_lo = bufferJson[7];
                            buffers.push_back(buffer);
                        }
                    }
                }

                UserInfo fromUser;
                auto fromUserObj = data["fromUser"];
                fromUser.id = fromUserObj["id"];
                fromUser.username = fromUserObj["username"];

                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (midiCallback_ && !buffers.empty() && !shouldStop_) {
                        midiCallback_(buffers, fromUser);
                    }
                }
            }
        }
        else if (type == "username_updated") {
            if (data.contains("username")) {
                std::string newUsername = data["username"];
                currentUser_.username = newUsername;

                if (usernameUpdatedCallback_ && !shouldStop_) {
                    usernameUpdatedCallback_(newUsername);
                }
            }
        }
        else if (type == "user_update") {
            if (data.contains("users")) {
                activeUsers_.clear();
                for (const auto& userJson : data["users"]) {
                    UserInfo user;
                    user.id = userJson["id"];
                    user.username = userJson["username"];
                    user.connectedClients = userJson.value("connectedClients", 1);
                    activeUsers_.push_back(user);
                }

                if (userUpdateCallback_ && !shouldStop_) {
                    userUpdateCallback_(activeUsers_);
                }
            }
        }
        else if (type == "error") {
            if (data.contains("message")) {
                std::string errorMessage = data["message"];

                if (errorCallback_ && !shouldStop_) {
                    errorCallback_(errorMessage);
                }
            }
        }
        else if (type == "pong") {
            if (pongCallback_ && !shouldStop_) {
                pongCallback_();
            }
        }
        else if (type == "chat") {
            if (data.contains("message") && data.contains("fromUser") && data.contains("timestamp")) {
                std::string message = data["message"];
                uint64_t timestamp = data["timestamp"];

                auto userObj = data["fromUser"];
                UserInfo fromUser;
                fromUser.id = userObj["id"];
                fromUser.username = userObj["username"];

                if (chatCallback_ && !shouldStop_) {
                    chatCallback_(message, fromUser, timestamp);
                }
            }
        }
        else if (type == "chat_history") {
            if (data.contains("messages")) {
                auto messages = data["messages"];
                for (const auto& msgData : messages) {
                    if (msgData.contains("message") && msgData.contains("fromUser") && msgData.contains("timestamp")) {
                        std::string message = msgData["message"];
                        uint64_t timestamp = msgData["timestamp"];

                        auto userObj = msgData["fromUser"];
                        UserInfo fromUser;
                        fromUser.id = userObj["id"];
                        fromUser.username = userObj["username"];

                        if (chatCallback_ && !shouldStop_) {
                            chatCallback_(message, fromUser, timestamp);
                        }
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        if (errorCallback_ && !shouldStop_) {
            errorCallback_("Failed to parse message: " + std::string(e.what()));
        }
    }
}

int WebSocketClient::lwsCallback(struct lws* wsi, enum lws_callback_reasons reason,
                                void* user, void* in, size_t len) {
    // Get context first and check if it's valid
    struct lws_context* context = lws_get_context(wsi);
    if (!context) {
        std::cerr << "lwsCallback: Invalid context" << std::endl;
        return 0;
    }

    WebSocketClient* client = static_cast<WebSocketClient*>(lws_context_user(context));

    // Safety check: ensure client is valid and not being destroyed
    if (!client) {
        std::cerr << "lwsCallback: Invalid client pointer" << std::endl;
        return 0;
    }

    // Additional safety check with mutex
    {
        std::lock_guard<std::mutex> lock(client->stateMutex_);
        if (client->shouldStop_) {
            std::cerr << "lwsCallback: Client is stopping, ignoring callback reason " << reason << std::endl;
            return 0;
        }
    }

    switch (reason) {
        case LWS_CALLBACK_CLIENT_ESTABLISHED:
            {
                std::cerr << "lwsCallback: CLIENT_ESTABLISHED" << std::endl;
                std::lock_guard<std::mutex> lock(client->stateMutex_);
                if (!client->shouldStop_) {
                    client->connected_ = true;
                    std::cerr << "lwsCallback: Connection established successfully, connected_=true" << std::endl;
                    // Surface transport-connected status to UI
                    if (client->errorCallback_) client->errorCallback_("Transport connected");
            // If MPP mode, send handshake; wrap with Engine.IO if必要
                    if (client->mppMode_) {
                        std::cerr << "Sending handshake... (engineIoMode=" << client->engineIoMode_ << ")" << std::endl;
                        if (client->engineIoMode_) {
                            // Engine.IO open probe: send 40 (Socket.IO open)
                            client->queueRaw("40");
                        } else {
                            nlohmann::json hiMsg; hiMsg["m"] = "hi"; client->queueMessage(hiMsg);
                            client->joinMPPChannelNow();
                            client->maybeSendUserset();
                        }
                        lws_callback_on_writable(wsi);
                        if (client->context_) lws_cancel_service(client->context_);
                    }
                } else {
                    std::cerr << "lwsCallback: Connection established but shouldStop_=true, not setting connected_" << std::endl;
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_RECEIVE:
            if (client && !client->shouldStop_ && in && len > 0) {
                std::string message(static_cast<char*>(in), len);

                {
                    std::lock_guard<std::mutex> lock(client->messageMutex_);

                    // Don't process messages if stopping
                    if (client->shouldStop_) {
                        break;
                    }

                    // Increased queue size for better buffering (support 100+ users)
                    const size_t maxIncomingQueueSize = 1000;
                    if (client->incomingMessages_.size() >= maxIncomingQueueSize) {
                        // Remove oldest messages if queue is full
                        while (client->incomingMessages_.size() >= maxIncomingQueueSize) {
                            client->incomingMessages_.pop();
                        }
                    }

                    client->incomingMessages_.push(message);
                }

                // Wake up message processing thread immediately
                if (!client->shouldStop_) {
                    client->messageCondition_.notify_one();
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_CONNECTION_ERROR:
            {
                std::cerr << "lwsCallback: CLIENT_CONNECTION_ERROR" << std::endl;
                client->handleConnectionLoss("Connection error");
            }
            break;

        case LWS_CALLBACK_CLOSED:
            {
                std::cerr << "lwsCallback: CLOSED" << std::endl;
                client->handleConnectionLoss("Server disconnected");
            }
            break;

        case LWS_CALLBACK_WSI_DESTROY:
            {
                std::cerr << "lwsCallback: WSI_DESTROY" << std::endl;
                // WSI is being destroyed - just clean up the pointer
                {
                    std::lock_guard<std::mutex> lock(client->stateMutex_);
                    client->wsi_ = nullptr;
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_WRITEABLE:
            // Process outgoing message queue when socket is writable
            if (client && !client->shouldStop_) {
                client->processOutgoingQueue();

                // Request callback again if there are more messages, but with rate limiting
                {
                    std::lock_guard<std::mutex> lock(client->outgoingMutex_);
                    if (!client->outgoingMessages_.empty()) {
                        // Only request writable if we haven't requested recently
                        auto now = std::chrono::steady_clock::now();
                        static auto lastWritableRequest = std::chrono::steady_clock::now();
                        if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastWritableRequest).count() > 1) {
                            lws_callback_on_writable(wsi);
                            lastWritableRequest = now;
                        }
                    }
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_CLOSED:
            {
                std::cerr << "lwsCallback: CLIENT_CLOSED" << std::endl;
                client->handleConnectionLoss("Connection closed");
            }
            break;

        case LWS_CALLBACK_EVENT_WAIT_CANCELLED:
            // This is normal - just means the event loop was cancelled
            break;
        case LWS_CALLBACK_GET_THREAD_ID:
        case LWS_CALLBACK_LOCK_POLL:
        case LWS_CALLBACK_UNLOCK_POLL:
        case LWS_CALLBACK_ADD_POLL_FD:
        case LWS_CALLBACK_DEL_POLL_FD:
        case LWS_CALLBACK_CHANGE_MODE_POLL_FD:
            // These are internal libwebsockets callbacks, don't log them
            break;

        default:
            // Log only truly unhandled callbacks for debugging
            std::cerr << "lwsCallback: Unhandled callback reason " << reason << std::endl;
            break;
    }

    return 0;
}

// Auto-reconnection methods
void WebSocketClient::setAutoReconnect(bool enable) {
    autoReconnect_ = enable;
}

bool WebSocketClient::getAutoReconnect() const {
    return autoReconnect_;
}

void WebSocketClient::cleanupAfterDisconnect(bool setShouldStop) {
    std::cerr << "cleanupAfterDisconnect() called, setShouldStop=" << setShouldStop << std::endl;

    // Update state
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        if (setShouldStop) {
            shouldStop_ = true;
        }
        connected_ = false;
        wsi_ = nullptr;
    }

    // Stop MIDI batch thread
    stopMidiBatchThread();

    // Wake up message processing thread
    messageCondition_.notify_all();

    // Wait for threads to finish with timeout
    std::cerr << "Waiting for threads to finish..." << std::endl;
    if (serviceThread_) {
        if (serviceThread_->joinable()) {
            std::cerr << "Joining service thread..." << std::endl;

            // Simple timeout mechanism without std::async
            bool joined = false;
            std::thread timeoutThread([this, &joined]() {
                serviceThread_->join();
                joined = true;
            });

            // Wait for up to 2 seconds
            for (int i = 0; i < 20 && !joined; ++i) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }

            if (joined) {
                timeoutThread.join();
                std::cerr << "Service thread joined" << std::endl;
            } else {
                std::cerr << "Service thread join timed out, detaching..." << std::endl;
                serviceThread_->detach();
                timeoutThread.detach();
            }
        } else {
            std::cerr << "Service thread not joinable" << std::endl;
        }
        serviceThread_.reset();
        std::cerr << "Service thread reset" << std::endl;
    }

    if (messageProcessThread_) {
        if (messageProcessThread_->joinable()) {
            std::cerr << "Joining message process thread..." << std::endl;

            // Simple timeout mechanism without std::async
            bool joined = false;
            std::thread timeoutThread([this, &joined]() {
                messageProcessThread_->join();
                joined = true;
            });

            // Wait for up to 2 seconds
            for (int i = 0; i < 20 && !joined; ++i) {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }

            if (joined) {
                timeoutThread.join();
                std::cerr << "Message process thread joined" << std::endl;
            } else {
                std::cerr << "Message process thread join timed out, detaching..." << std::endl;
                messageProcessThread_->detach();
                timeoutThread.detach();
            }
        } else {
            std::cerr << "Message process thread not joinable" << std::endl;
        }
        messageProcessThread_.reset();
        std::cerr << "Message process thread reset" << std::endl;
    }

    // Clean up context
    std::cerr << "Cleaning up context..." << std::endl;
    if (context_) {
        lws_context_destroy(context_);
        context_ = nullptr;
        std::cerr << "Context destroyed and reset" << std::endl;
    }

    // Clear user state
    clientId_.clear();
    currentUser_ = UserInfo{};
    activeUsers_.clear();

    // Clear message queues
    {
        std::lock_guard<std::mutex> lock(messageMutex_);
        while (!incomingMessages_.empty()) {
            incomingMessages_.pop();
        }
    }

    {
        std::lock_guard<std::mutex> lock(outgoingMutex_);
        while (!outgoingMessages_.empty()) {
            outgoingMessages_.pop();
        }
    }

    // Handle reconnect thread (only for manual disconnect)
    if (setShouldStop && reconnectThread_) {
        if (reconnectThread_->joinable()) {
            // Check if we're being called from the reconnect thread itself
            if (reconnectThread_->get_id() == std::this_thread::get_id()) {
                std::cerr << "Called from reconnect thread itself, detaching..." << std::endl;
                reconnectThread_->detach();
            } else {
                std::cerr << "Joining reconnect thread..." << std::endl;

                // Simple timeout mechanism without std::async
                bool joined = false;
                std::thread timeoutThread([this, &joined]() {
                    reconnectThread_->join();
                    joined = true;
                });

                // Wait for up to 1 second
                for (int i = 0; i < 10 && !joined; ++i) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }

                if (joined) {
                    timeoutThread.join();
                    std::cerr << "Reconnect thread joined successfully" << std::endl;
                } else {
                    std::cerr << "Reconnect thread join timed out, detaching..." << std::endl;
                    reconnectThread_->detach();
                    timeoutThread.detach();
                }
            }
        }
        reconnectThread_.reset();
        std::cerr << "Reconnect thread reset" << std::endl;
    }

    std::cerr << "cleanupAfterDisconnect() completed" << std::endl;
}

void WebSocketClient::handleConnectionLoss(const std::string& reason) {
    std::cerr << "Connection lost: " << reason << std::endl;

    // Check if this is a manual disconnection or already handled
    bool isManualDisconnect = false;
    bool alreadyHandled = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        isManualDisconnect = shouldStop_;
        alreadyHandled = disconnectHandled_;

        // Mark as handled to prevent duplicate processing
        if (!alreadyHandled) {
            disconnectHandled_ = true;
        }
    }

    // Don't process if this was a manual disconnect or already handled
    if (isManualDisconnect || alreadyHandled) {
        std::cerr << "Manual disconnection or already handled, skipping reconnection" << std::endl;
        return;
    }

    std::cerr << "Server disconnection detected" << std::endl;

    // Call error callback to update UI
    if (errorCallback_) {
        std::cerr << "Calling error callback for server disconnect" << std::endl;
        errorCallback_(reason);
    }

    // Set connection state immediately
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        connected_ = false;
        wsi_ = nullptr;
    }

    // Attempt reconnection if enabled
    if (autoReconnect_ && !lastHost_.empty()) {
        std::cerr << "Scheduling auto-reconnection..." << std::endl;

        // Reset flags for reconnection
        {
            std::lock_guard<std::mutex> lock(stateMutex_);
            shouldStop_ = false;
            disconnectHandled_ = false;
        }

        // Start reconnection in a separate detached thread to avoid blocking
        std::thread([this]() {
            std::this_thread::sleep_for(std::chrono::milliseconds(100)); // Brief delay
            attemptReconnect();
        }).detach();
    }
}

void WebSocketClient::attemptReconnect() {
    std::cerr << "attemptReconnect() called" << std::endl;

    // Check if we should abort reconnection
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        if (shouldStop_) {
            std::cerr << "attemptReconnect: shouldStop_ is true, aborting" << std::endl;
            return;
        }
    }

    if (!autoReconnect_) {
        std::cerr << "attemptReconnect: autoReconnect_ is false, aborting" << std::endl;
        return;
    }

    if (lastHost_.empty()) {
        std::cerr << "attemptReconnect: lastHost_ is empty, aborting" << std::endl;
        return;
    }

    // Check if we've exceeded max attempts
    if (reconnectAttempts_ >= maxReconnectAttempts_) {
        std::cerr << "Max reconnection attempts reached. Giving up." << std::endl;
        autoReconnect_ = false;
        if (errorCallback_) {
            errorCallback_("Connection failed - max " + std::to_string(maxReconnectAttempts_) + " attempts reached");
        }
        return;
    }

    reconnectAttempts_++;

    // Calculate exponential backoff delay: 500ms, 1s, 2s, 4s, 8s, 16s, 32s, 60s, 60s, ...
    int delayMs = std::min(
        static_cast<int>(initialReconnectDelay_.count() * (1 << (reconnectAttempts_ - 1))),
        maxReconnectDelay_ * 1000  // Convert max delay to milliseconds
    );
    int delaySeconds = (delayMs + 999) / 1000;  // Round up to nearest second for display

    std::cerr << "Attempting reconnection " << reconnectAttempts_ << "/" << maxReconnectAttempts_
              << " in " << delayMs << "ms (" << delaySeconds << " seconds)..." << std::endl;

    // Update GUI status for reconnection attempt
    if (errorCallback_) {
        if (delayMs < 1000) {
            errorCallback_("Reconnection attempt " + std::to_string(reconnectAttempts_) + "/" +
                          std::to_string(maxReconnectAttempts_) + " in " + std::to_string(delayMs) + "ms...");
        } else {
            errorCallback_("Reconnection attempt " + std::to_string(reconnectAttempts_) + "/" +
                          std::to_string(maxReconnectAttempts_) + " in " + std::to_string(delaySeconds) + " seconds...");
        }
    }

    // Start reconnection in a separate thread to avoid blocking
    if (reconnectThread_ && reconnectThread_->joinable()) {
        reconnectThread_->join();
    }

    reconnectThread_ = std::make_unique<std::thread>([this, delayMs, delaySeconds]() {
        std::cerr << "Reconnect thread started, delayMs=" << delayMs << std::endl;

        // Wait with status updates
        if (delayMs < 1000) {
            // For delays less than 1 second, show milliseconds and wait without countdown
            if (errorCallback_) {
                errorCallback_("Reconnecting in " + std::to_string(delayMs) + "ms...");
            }

            // Sleep in small chunks to allow for early termination
            int remainingMs = delayMs;
            while (remainingMs > 0) {
                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (shouldStop_ || !autoReconnect_) {
                        std::cerr << "Reconnect thread stopping early: shouldStop_=" << shouldStop_ << ", autoReconnect_=" << autoReconnect_ << std::endl;
                        return;
                    }
                }

                int sleepMs = std::min(remainingMs, 50);  // Sleep in 50ms chunks
                std::this_thread::sleep_for(std::chrono::milliseconds(sleepMs));
                remainingMs -= sleepMs;
            }
        } else {
            // For delays 1 second or more, show countdown in seconds
            for (int i = delaySeconds; i > 0; i--) {
                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (shouldStop_ || !autoReconnect_) {
                        std::cerr << "Reconnect thread stopping early: shouldStop_=" << shouldStop_ << ", autoReconnect_=" << autoReconnect_ << std::endl;
                        return;
                    }
                }

                // Update status with countdown
                if (errorCallback_) {
                    errorCallback_("Reconnecting in " + std::to_string(i) + " seconds...");
                }

                // Sleep in smaller chunks to be more responsive to stop signals
                for (int j = 0; j < 10; j++) {
                    {
                        std::lock_guard<std::mutex> lock(stateMutex_);
                        if (shouldStop_ || !autoReconnect_) {
                            return;
                        }
                    }
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
            }
        }

        std::cerr << "Countdown completed, attempting connection..." << std::endl;

        // Final check before attempting connection
        {
            std::lock_guard<std::mutex> lock(stateMutex_);
            if (shouldStop_ || !autoReconnect_) {
                std::cerr << "Reconnect aborted before connection attempt" << std::endl;
                return;
            }
        }

        // Update status to show connection attempt
        if (errorCallback_) {
            errorCallback_("Connecting to " + lastHost_ + ":" + std::to_string(lastPort_) + "...");
        }

        // Clean up any existing connection state before reconnecting
        if (context_) {
            lws_context_destroy(context_);
            context_ = nullptr;
        }

        bool connectionSuccess = false;
        try {
            connectionSuccess = connect(lastHost_, lastPort_);
        } catch (const std::exception& e) {
            std::cerr << "Exception during reconnection: " << e.what() << std::endl;
            connectionSuccess = false;
        }

        if (connectionSuccess) {
            std::cerr << "Reconnection successful!" << std::endl;
            reconnectAttempts_ = 0; // Reset on successful connection

            // Update GUI status for successful reconnection
            if (errorCallback_) {
                errorCallback_("Reconnected successfully!");
            }
        } else {
            std::cerr << "Reconnection attempt " << reconnectAttempts_ << " failed." << std::endl;

            // Update GUI status for failed reconnection
            if (errorCallback_) {
                if (reconnectAttempts_ >= maxReconnectAttempts_) {
                    errorCallback_("Reconnection failed after " + std::to_string(maxReconnectAttempts_) + " attempts");
                } else {
                    errorCallback_("Reconnection attempt " + std::to_string(reconnectAttempts_) + " failed, retrying...");
                }
            }

            // Schedule next attempt if still enabled
            {
                std::lock_guard<std::mutex> lock(stateMutex_);
                if (autoReconnect_ && !shouldStop_) {
                    // Use a separate thread for the next attempt to avoid stack overflow
                    std::thread([this]() {
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));
                        attemptReconnect();
                    }).detach();
                }
            }
        }
    });
}

void WebSocketClient::startMidiBatchThread() {
    std::cerr << "Starting MIDI batch thread..." << std::endl;

    // Stop any existing thread first
    stopMidiBatchThread();

    midiBatchThread_ = std::make_unique<std::thread>([this]() {
        midiBatchThreadFunction();
    });
}

void WebSocketClient::stopMidiBatchThread() {
    if (midiBatchThread_ && midiBatchThread_->joinable()) {
        std::cerr << "Stopping MIDI batch thread..." << std::endl;

        // Flush any pending MIDI data before stopping
        flushMidiBatch();

        // Simple timeout mechanism without std::async
        bool joined = false;
        std::thread timeoutThread([this, &joined]() {
            midiBatchThread_->join();
            joined = true;
        });

        // Wait for up to 1 second
        for (int i = 0; i < 10 && !joined; ++i) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        if (joined) {
            timeoutThread.join();
        } else {
            std::cerr << "MIDI batch thread join timed out, detaching..." << std::endl;
            midiBatchThread_->detach();
            timeoutThread.detach();
        }

        midiBatchThread_.reset();
        std::cerr << "MIDI batch thread stopped" << std::endl;
    }
}

void WebSocketClient::midiBatchThreadFunction() {
    std::cerr << "MIDI batch thread started" << std::endl;

    while (!shouldStop_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(midiBatchTimeoutMs_));

        // Check if we should stop
        if (shouldStop_) {
            break;
        }

        // Process any pending MIDI data that has been waiting
        {
            std::lock_guard<std::mutex> lock(midiBatchMutex_);
            auto now = std::chrono::steady_clock::now();
            auto timeSinceLastBatch = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastMidiBatchTime_);

            // If we have pending data and enough time has passed, send it
            if (!pendingMidiBuffers_.empty() && timeSinceLastBatch.count() >= midiBatchTimeoutMs_) {
                processPendingMidiBatch();
            }
        }
    }

    std::cerr << "MIDI batch thread ending" << std::endl;
}

void WebSocketClient::processPendingMidiBatch() {
    // This method should be called with midiBatchMutex_ already locked
    if (pendingMidiBuffers_.empty()) {
        return;
    }

    // Log batching information for debugging (commented for performance with large groups)
    // std::cerr << "Sending MIDI batch with " << pendingMidiBuffers_.size() << " notes" << std::endl;

    // Send the batched MIDI data using the existing sendMidi method
    sendMidi(pendingMidiBuffers_);

    // Clear the pending buffers and update timestamp
    pendingMidiBuffers_.clear();
    lastMidiBatchTime_ = std::chrono::steady_clock::now();
}

// MIDI batching configuration methods
void WebSocketClient::setMidiBatchTimeout(int timeoutMs) {
    std::lock_guard<std::mutex> lock(midiBatchMutex_);
    midiBatchTimeoutMs_ = std::max(1, std::min(timeoutMs, 100)); // Limit to 1-100ms
}

void WebSocketClient::setMidiBatchSize(size_t maxSize) {
    std::lock_guard<std::mutex> lock(midiBatchMutex_);
    maxMidiBatchSize_ = std::max(size_t(1), std::min(maxSize, size_t(100))); // Limit to 1-100 notes
}

int WebSocketClient::getMidiBatchTimeout() const {
    return midiBatchTimeoutMs_;
}

size_t WebSocketClient::getMidiBatchSize() const {
    return maxMidiBatchSize_;
}

// Compression/decompression utilities
std::vector<uint8_t> WebSocketClient::compressData(const std::vector<uint8_t>& data) {
    if (data.empty()) {
        return {};
    }

    // Initialize zlib compression
    z_stream stream;
    memset(&stream, 0, sizeof(stream));

    // Initialize deflate with default compression level
    if (deflateInit(&stream, Z_DEFAULT_COMPRESSION) != Z_OK) {
        std::cerr << "Failed to initialize zlib compression" << std::endl;
        return {};
    }

    // Set input data
    stream.avail_in = static_cast<uInt>(data.size());
    stream.next_in = const_cast<Bytef*>(data.data());

    // Prepare output buffer (worst case: input size + 12 bytes overhead)
    std::vector<uint8_t> compressed;
    compressed.resize(data.size() + 12);

    stream.avail_out = static_cast<uInt>(compressed.size());
    stream.next_out = compressed.data();

    // Perform compression
    int result = deflate(&stream, Z_FINISH);

    if (result != Z_STREAM_END) {
        std::cerr << "Failed to compress data: " << result << std::endl;
        deflateEnd(&stream);
        return {};
    }

    // Resize to actual compressed size
    compressed.resize(compressed.size() - stream.avail_out);

    deflateEnd(&stream);
    return compressed;
}

std::vector<uint8_t> WebSocketClient::decompressData(const std::vector<uint8_t>& compressedData) {
    if (compressedData.empty()) {
        return {};
    }

    // Initialize zlib decompression
    z_stream stream;
    memset(&stream, 0, sizeof(stream));

    if (inflateInit(&stream) != Z_OK) {
        std::cerr << "Failed to initialize zlib decompression" << std::endl;
        return {};
    }

    // Set input data
    stream.avail_in = static_cast<uInt>(compressedData.size());
    stream.next_in = const_cast<Bytef*>(compressedData.data());

    // Start with a reasonable buffer size
    std::vector<uint8_t> decompressed;
    size_t bufferSize = std::max(static_cast<size_t>(1024), compressedData.size() * 4);
    decompressed.resize(bufferSize);

    stream.avail_out = static_cast<uInt>(decompressed.size());
    stream.next_out = decompressed.data();

    // Perform decompression with retry logic
    int result = inflate(&stream, Z_FINISH);

    // If buffer was too small, try with progressively larger buffers
    int retries = 0;
    while (result == Z_BUF_ERROR && retries < 3) {
        // Reset stream for retry
        inflateEnd(&stream);
        memset(&stream, 0, sizeof(stream));
        if (inflateInit(&stream) != Z_OK) {
            std::cerr << "Failed to reinitialize zlib decompression" << std::endl;
            return {};
        }

        // Increase buffer size
        bufferSize *= 2;
        decompressed.resize(bufferSize);

        // Reset stream pointers
        stream.avail_in = static_cast<uInt>(compressedData.size());
        stream.next_in = const_cast<Bytef*>(compressedData.data());
        stream.avail_out = static_cast<uInt>(decompressed.size());
        stream.next_out = decompressed.data();

        result = inflate(&stream, Z_FINISH);
        retries++;
    }

    if (result != Z_STREAM_END) {
        std::cerr << "Failed to decompress data: " << result
                  << " (compressed size: " << compressedData.size() << " bytes)" << std::endl;
        inflateEnd(&stream);
        return {};
    }

    // Resize to actual decompressed size
    decompressed.resize(decompressed.size() - stream.avail_out);

    inflateEnd(&stream);
    return decompressed;
}
