#include "opengl_renderer.h"
#include <iostream>
#include <cmath>
#include <vector>
#include <fstream>
#include <cstring>

#define STB_IMAGE_IMPLEMENTATION
#include "stb_image.h"

#define STB_TRUETYPE_IMPLEMENTATION
#include "stb_truetype.h"
#include "embedded_fonts.h"

OpenGLRenderer::OpenGLRenderer() 
    : window_width_(800), window_height_(600) {
}

OpenGLRenderer::~OpenGLRenderer() {
}

void OpenGLRenderer::Initialize(int window_width, int window_height) {
    window_width_ = window_width;
    window_height_ = window_height;
    
    // Enable blending for transparency
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    
    SetupProjection();
}

void OpenGLRenderer::SetViewport(int width, int height) {
    window_width_ = width;
    window_height_ = height;
    glViewport(0, 0, width, height);
    SetupProjection();
}

void OpenGLRenderer::Clear(const Color& clear_color) {
    glClearColor(clear_color.r, clear_color.g, clear_color.b, clear_color.a);
    glClear(GL_COLOR_BUFFER_BIT);
}

void OpenGLRenderer::ClearWithRadialGradient(const Color& center_color, const Color& edge_color) {
    // Clear with edge color first
    glClearColor(edge_color.r, edge_color.g, edge_color.b, edge_color.a);
    glClear(GL_COLOR_BUFFER_BIT);

    // Draw radial gradient using triangle fan (elliptical to match window aspect ratio)
    const int segments = 100; // Number of segments for smooth ellipse
    const float pi = 3.14159265359f;

    // Calculate center position and ellipse radii
    float center_x = window_width_ * 0.5f;
    float center_y = window_height_ * 0.5f;
    float radius_x = window_width_ * 0.8f;  // Horizontal radius based on window width
    float radius_y = window_height_ * 0.8f; // Vertical radius based on window height

    glBegin(GL_TRIANGLE_FAN);

    // Center vertex with center color
    glColor4f(center_color.r, center_color.g, center_color.b, center_color.a);
    glVertex2f(center_x, center_y);

    // Edge vertices with edge color (elliptical shape)
    glColor4f(edge_color.r, edge_color.g, edge_color.b, edge_color.a);
    for (int i = 0; i <= segments; i++) {
        float angle = 2.0f * pi * i / segments;
        float x = center_x + radius_x * cos(angle);
        float y = center_y + radius_y * sin(angle);
        glVertex2f(x, y);
    }

    glEnd();
}



void OpenGLRenderer::ClearWithImage(const std::string& image_path, float opacity, int scale_mode) {
    // Clear with black background first
    glClearColor(0.0f, 0.0f, 0.0f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT);

    // Check if image path is empty
    if (image_path.empty()) {
        // No image specified, just use black background
        return;
    }

    // Load image if not already loaded or path changed
    if (!background_image_.loaded || background_image_.path != image_path) {
        if (LoadImageTexture(image_path, background_image_)) {
            background_image_.path = image_path;
            background_image_.loaded = true;
        } else {
            // Failed to load image, reset the loaded flag and use black background
            background_image_.loaded = false;
            background_image_.path = "";
            return;
        }
    }

    // Draw the background image
    if (background_image_.loaded && background_image_.texture_id != 0) {
        DrawImageBackground(background_image_, opacity, scale_mode);
    }
}

void OpenGLRenderer::DrawRect(const Vec2& position, const Vec2& size, const Color& color) {
    // Enable blending for transparency
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    glColor4f(color.r, color.g, color.b, color.a);
    DrawQuad(position, size);

    // Disable blending after drawing
    glDisable(GL_BLEND);
}

void OpenGLRenderer::DrawRectGradient(const Vec2& position, const Vec2& size,
                                     const Color& top_color, const Color& bottom_color) {
    glBegin(GL_QUADS);

    // Top-left vertex (top color)
    glColor4f(top_color.r, top_color.g, top_color.b, top_color.a);
    glVertex2f(position.x, position.y);

    // Top-right vertex (top color)
    glColor4f(top_color.r, top_color.g, top_color.b, top_color.a);
    glVertex2f(position.x + size.x, position.y);

    // Bottom-right vertex (bottom color)
    glColor4f(bottom_color.r, bottom_color.g, bottom_color.b, bottom_color.a);
    glVertex2f(position.x + size.x, position.y + size.y);

    // Bottom-left vertex (bottom color)
    glColor4f(bottom_color.r, bottom_color.g, bottom_color.b, bottom_color.a);
    glVertex2f(position.x, position.y + size.y);

    glEnd();
}

void OpenGLRenderer::DrawRectGradientRounded(const Vec2& position, const Vec2& size,
                                            const Color& top_color, const Color& bottom_color,
                                            float corner_radius) {
    const int segments = 8; // Number of segments for quarter circle
    const float pi = 3.14159265359f;

    // Helper function to interpolate colors based on Y position
    auto interpolateColor = [&](float y) -> Color {
        float t = (y - position.y) / size.y; // Normalize Y position (0 = top, 1 = bottom)
        t = std::max(0.0f, std::min(1.0f, t)); // Clamp to [0, 1]

        return Color(
            top_color.r + t * (bottom_color.r - top_color.r),
            top_color.g + t * (bottom_color.g - top_color.g),
            top_color.b + t * (bottom_color.b - top_color.b),
            top_color.a + t * (bottom_color.a - top_color.a)
        );
    };

    glBegin(GL_TRIANGLE_FAN);

    // Center point for fan triangulation
    float center_x = position.x + size.x * 0.5f;
    float center_y = position.y + size.y * 0.5f;
    Color center_color = interpolateColor(center_y);
    glColor4f(center_color.r, center_color.g, center_color.b, center_color.a);
    glVertex2f(center_x, center_y);

    // Draw rounded rectangle as triangle fan
    std::vector<Vec2> vertices;

    // Top-left corner
    for (int i = 0; i <= segments; i++) {
        float angle = pi + (pi * 0.5f * i) / segments;
        float x = position.x + corner_radius + corner_radius * cos(angle);
        float y = position.y + corner_radius + corner_radius * sin(angle);
        vertices.push_back(Vec2(x, y));
    }

    // Top-right corner
    for (int i = 0; i <= segments; i++) {
        float angle = 1.5f * pi + (pi * 0.5f * i) / segments;
        float x = position.x + size.x - corner_radius + corner_radius * cos(angle);
        float y = position.y + corner_radius + corner_radius * sin(angle);
        vertices.push_back(Vec2(x, y));
    }

    // Bottom-right corner
    for (int i = 0; i <= segments; i++) {
        float angle = 0.0f + (pi * 0.5f * i) / segments;
        float x = position.x + size.x - corner_radius + corner_radius * cos(angle);
        float y = position.y + size.y - corner_radius + corner_radius * sin(angle);
        vertices.push_back(Vec2(x, y));
    }

    // Bottom-left corner
    for (int i = 0; i <= segments; i++) {
        float angle = 0.5f * pi + (pi * 0.5f * i) / segments;
        float x = position.x + corner_radius + corner_radius * cos(angle);
        float y = position.y + size.y - corner_radius + corner_radius * sin(angle);
        vertices.push_back(Vec2(x, y));
    }

    // Draw all vertices with interpolated colors
    for (const auto& vertex : vertices) {
        Color vertex_color = interpolateColor(vertex.y);
        glColor4f(vertex_color.r, vertex_color.g, vertex_color.b, vertex_color.a);
        glVertex2f(vertex.x, vertex.y);
    }

    // Close the fan by repeating the first vertex
    if (!vertices.empty()) {
        Color first_color = interpolateColor(vertices[0].y);
        glColor4f(first_color.r, first_color.g, first_color.b, first_color.a);
        glVertex2f(vertices[0].x, vertices[0].y);
    }

    glEnd();
}

void OpenGLRenderer::DrawRectWithBorder(const Vec2& position, const Vec2& size,
                                       const Color& fill_color, const Color& border_color,
                                       float border_width) {
    // Draw filled rectangle only if not transparent
    if (fill_color.a > 0.0f) {
        DrawRect(position, size, fill_color);
    }

    // Draw border with smooth lines
    glColor4f(border_color.r, border_color.g, border_color.b, border_color.a);
    glLineWidth(border_width);

    // Enable line smoothing for rounded appearance
    glEnable(GL_LINE_SMOOTH);
    glHint(GL_LINE_SMOOTH_HINT, GL_NICEST);

    glBegin(GL_LINE_LOOP);
    glVertex2f(position.x, position.y);
    glVertex2f(position.x + size.x, position.y);
    glVertex2f(position.x + size.x, position.y + size.y);
    glVertex2f(position.x, position.y + size.y);
    glEnd();

    glDisable(GL_LINE_SMOOTH);
}

void OpenGLRenderer::DrawRectWithRoundedBorder(const Vec2& position, const Vec2& size,
                                              const Color& fill_color, const Color& border_color,
                                              float border_width, float corner_radius) {
    // Draw filled rectangle only if not transparent
    if (fill_color.a > 0.0f) {
        DrawRect(position, size, fill_color);
    }

    // Draw rounded border
    glColor4f(border_color.r, border_color.g, border_color.b, border_color.a);
    glLineWidth(border_width);

    // Enable line smoothing for better appearance
    glEnable(GL_LINE_SMOOTH);
    glHint(GL_LINE_SMOOTH_HINT, GL_NICEST);

    const int segments = 8; // Number of segments for quarter circle
    const float pi = 3.14159265359f;

    glBegin(GL_LINE_STRIP);

    // Top-left corner
    for (int i = 0; i <= segments; i++) {
        float angle = pi + (pi * 0.5f * i) / segments;
        float x = position.x + corner_radius + corner_radius * cos(angle);
        float y = position.y + corner_radius + corner_radius * sin(angle);
        glVertex2f(x, y);
    }

    // Top-right corner
    for (int i = 0; i <= segments; i++) {
        float angle = 1.5f * pi + (pi * 0.5f * i) / segments;
        float x = position.x + size.x - corner_radius + corner_radius * cos(angle);
        float y = position.y + corner_radius + corner_radius * sin(angle);
        glVertex2f(x, y);
    }

    // Bottom-right corner
    for (int i = 0; i <= segments; i++) {
        float angle = 0.0f + (pi * 0.5f * i) / segments;
        float x = position.x + size.x - corner_radius + corner_radius * cos(angle);
        float y = position.y + size.y - corner_radius + corner_radius * sin(angle);
        glVertex2f(x, y);
    }

    // Bottom-left corner
    for (int i = 0; i <= segments; i++) {
        float angle = 0.5f * pi + (pi * 0.5f * i) / segments;
        float x = position.x + corner_radius + corner_radius * cos(angle);
        float y = position.y + size.y - corner_radius + corner_radius * sin(angle);
        glVertex2f(x, y);
    }

    // Close the loop
    float angle = pi;
    float x = position.x + corner_radius + corner_radius * cos(angle);
    float y = position.y + corner_radius + corner_radius * sin(angle);
    glVertex2f(x, y);

    glEnd();
    glDisable(GL_LINE_SMOOTH);
}

void OpenGLRenderer::BeginBatch() {
    batch_rects_.clear();
}

void OpenGLRenderer::AddRect(const Rect& rect) {
    batch_rects_.push_back(rect);
}

void OpenGLRenderer::EndBatch() {
    for (const auto& rect : batch_rects_) {
        DrawRectWithBorder(rect.position, rect.size, rect.color, 
                          rect.border_color, rect.border_width);
    }
    batch_rects_.clear();
}

Vec2 OpenGLRenderer::ScreenToGL(const Vec2& screen_pos) const {
    // Convert from screen coordinates (0,0 top-left) to OpenGL coordinates (-1,-1 bottom-left to 1,1 top-right)
    float gl_x = (screen_pos.x / window_width_) * 2.0f - 1.0f;
    float gl_y = 1.0f - (screen_pos.y / window_height_) * 2.0f;
    return Vec2(gl_x, gl_y);
}

Vec2 OpenGLRenderer::GLToScreen(const Vec2& gl_pos) const {
    // Convert from OpenGL coordinates to screen coordinates
    float screen_x = (gl_pos.x + 1.0f) * 0.5f * window_width_;
    float screen_y = (1.0f - gl_pos.y) * 0.5f * window_height_;
    return Vec2(screen_x, screen_y);
}

void OpenGLRenderer::DrawQuad(const Vec2& position, const Vec2& size) {
    glBegin(GL_QUADS);
    glVertex2f(position.x, position.y);
    glVertex2f(position.x + size.x, position.y);
    glVertex2f(position.x + size.x, position.y + size.y);
    glVertex2f(position.x, position.y + size.y);
    glEnd();
}

void OpenGLRenderer::SetupProjection() {
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    
    // Use screen coordinates (0,0 top-left to width,height bottom-right)
    glOrtho(0.0, window_width_, window_height_, 0.0, -1.0, 1.0);
    
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();
}

bool OpenGLRenderer::LoadImageTexture(const std::string& path, BackgroundImage& image) {
    // Check if path is empty
    if (path.empty()) {
        return false;
    }

    // Load image using stb_image (supports PNG, JPEG, BMP, TGA, etc.)
    int width, height, channels;
    unsigned char* data = stbi_load(path.c_str(), &width, &height, &channels, 0);

    if (!data) {
        // Only show error message if this is a new path (not repeated attempts)
        static std::string last_failed_path;
        if (last_failed_path != path) {
            std::cerr << "Failed to load image file: " << path << " - " << stbi_failure_reason() << std::endl;
            last_failed_path = path;
        }
        return false;
    }

    // Delete old texture if exists
    if (image.texture_id != 0) {
        glDeleteTextures(1, &image.texture_id);
    }

    // Generate OpenGL texture
    glGenTextures(1, &image.texture_id);
    glBindTexture(GL_TEXTURE_2D, image.texture_id);

    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);

    // Determine format based on number of channels
    GLenum format;
    if (channels == 1) {
        format = GL_LUMINANCE;
    } else if (channels == 3) {
        format = GL_RGB;
    } else if (channels == 4) {
        format = GL_RGBA;
    } else {
        std::cerr << "Unsupported number of channels: " << channels << std::endl;
        stbi_image_free(data);
        return false;
    }

    glTexImage2D(GL_TEXTURE_2D, 0, format, width, height, 0, format, GL_UNSIGNED_BYTE, data);

    // Free the image data
    stbi_image_free(data);

    image.width = width;
    image.height = height;

    std::cout << "Loaded background image: " << path << " (" << width << "x" << height << ", " << channels << " channels)" << std::endl;
    return true;
}

void OpenGLRenderer::DrawImageBackground(const BackgroundImage& image, float opacity, int scale_mode) {
    if (image.texture_id == 0) return;

    glEnable(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, image.texture_id);
    glColor4f(1.0f, 1.0f, 1.0f, opacity);

    float tex_coords[8];
    float vertices[8];

    // Calculate texture coordinates and vertices based on scale mode
    switch (scale_mode) {
        case 0: // Stretch - stretch image to fill window
            tex_coords[0] = 0.0f; tex_coords[1] = 1.0f; // bottom-left
            tex_coords[2] = 1.0f; tex_coords[3] = 1.0f; // bottom-right
            tex_coords[4] = 1.0f; tex_coords[5] = 0.0f; // top-right
            tex_coords[6] = 0.0f; tex_coords[7] = 0.0f; // top-left

            vertices[0] = 0.0f; vertices[1] = (float)window_height_;
            vertices[2] = (float)window_width_; vertices[3] = (float)window_height_;
            vertices[4] = (float)window_width_; vertices[5] = 0.0f;
            vertices[6] = 0.0f; vertices[7] = 0.0f;
            break;

        case 1: // Fit - maintain aspect ratio, fit within window
            {
                float window_aspect = (float)window_width_ / (float)window_height_;
                float image_aspect = (float)image.width / (float)image.height;

                float scale = (window_aspect > image_aspect) ?
                    (float)window_height_ / (float)image.height :
                    (float)window_width_ / (float)image.width;

                float scaled_width = image.width * scale;
                float scaled_height = image.height * scale;
                float offset_x = (window_width_ - scaled_width) * 0.5f;
                float offset_y = (window_height_ - scaled_height) * 0.5f;

                tex_coords[0] = 0.0f; tex_coords[1] = 1.0f;
                tex_coords[2] = 1.0f; tex_coords[3] = 1.0f;
                tex_coords[4] = 1.0f; tex_coords[5] = 0.0f;
                tex_coords[6] = 0.0f; tex_coords[7] = 0.0f;

                vertices[0] = offset_x; vertices[1] = offset_y + scaled_height;
                vertices[2] = offset_x + scaled_width; vertices[3] = offset_y + scaled_height;
                vertices[4] = offset_x + scaled_width; vertices[5] = offset_y;
                vertices[6] = offset_x; vertices[7] = offset_y;
            }
            break;

        case 2: // Fill - maintain aspect ratio, fill window (may crop)
            {
                float window_aspect = (float)window_width_ / (float)window_height_;
                float image_aspect = (float)image.width / (float)image.height;

                if (window_aspect > image_aspect) {
                    // Window is wider, crop top/bottom of image
                    float tex_height = image_aspect / window_aspect;
                    float tex_offset = (1.0f - tex_height) * 0.5f;

                    tex_coords[0] = 0.0f; tex_coords[1] = 1.0f - tex_offset;
                    tex_coords[2] = 1.0f; tex_coords[3] = 1.0f - tex_offset;
                    tex_coords[4] = 1.0f; tex_coords[5] = tex_offset;
                    tex_coords[6] = 0.0f; tex_coords[7] = tex_offset;
                } else {
                    // Window is taller, crop left/right of image
                    float tex_width = window_aspect / image_aspect;
                    float tex_offset = (1.0f - tex_width) * 0.5f;

                    tex_coords[0] = tex_offset; tex_coords[1] = 1.0f;
                    tex_coords[2] = 1.0f - tex_offset; tex_coords[3] = 1.0f;
                    tex_coords[4] = 1.0f - tex_offset; tex_coords[5] = 0.0f;
                    tex_coords[6] = tex_offset; tex_coords[7] = 0.0f;
                }

                vertices[0] = 0.0f; vertices[1] = (float)window_height_;
                vertices[2] = (float)window_width_; vertices[3] = (float)window_height_;
                vertices[4] = (float)window_width_; vertices[5] = 0.0f;
                vertices[6] = 0.0f; vertices[7] = 0.0f;
            }
            break;

        case 3: // Tile - repeat image to fill window
            {
                float tile_x = (float)window_width_ / (float)image.width;
                float tile_y = (float)window_height_ / (float)image.height;

                tex_coords[0] = 0.0f; tex_coords[1] = tile_y;
                tex_coords[2] = tile_x; tex_coords[3] = tile_y;
                tex_coords[4] = tile_x; tex_coords[5] = 0.0f;
                tex_coords[6] = 0.0f; tex_coords[7] = 0.0f;

                vertices[0] = 0.0f; vertices[1] = (float)window_height_;
                vertices[2] = (float)window_width_; vertices[3] = (float)window_height_;
                vertices[4] = (float)window_width_; vertices[5] = 0.0f;
                vertices[6] = 0.0f; vertices[7] = 0.0f;
            }
            break;
    }

    // Draw textured quad
    glBegin(GL_QUADS);
    for (int i = 0; i < 4; i++) {
        glTexCoord2f(tex_coords[i*2], tex_coords[i*2+1]);
        glVertex2f(vertices[i*2], vertices[i*2+1]);
    }
    glEnd();

    glDisable(GL_TEXTURE_2D);
}

bool OpenGLRenderer::LoadFont(float font_size) {
    if (font_atlas_.loaded) {
        return true; // Already loaded
    }

    // Initialize STB TrueType
    stbtt_fontinfo font;
    if (!stbtt_InitFont(&font, GothicA1_Regular_ttf_data, 0)) {
        std::cerr << "Failed to initialize font" << std::endl;
        return false;
    }

    font_atlas_.font_size = font_size;

    // Create bitmap atlas
    int atlas_size = 512; // 512x512 texture atlas
    font_atlas_.atlas_width = atlas_size;
    font_atlas_.atlas_height = atlas_size;

    std::vector<unsigned char> atlas_bitmap(atlas_size * atlas_size);

    // Pack ASCII characters 32-126 into the atlas
    stbtt_pack_context pack_context;
    stbtt_PackBegin(&pack_context, atlas_bitmap.data(), atlas_size, atlas_size, 0, 1, nullptr);
    stbtt_PackSetOversampling(&pack_context, 1, 1); // No oversampling for simplicity

    stbtt_packedchar packed_chars[95]; // 95 printable ASCII characters
    stbtt_PackFontRange(&pack_context, GothicA1_Regular_ttf_data, 0, font_size, 32, 95, packed_chars);
    stbtt_PackEnd(&pack_context);

    // Create OpenGL texture
    glGenTextures(1, &font_atlas_.texture_id);
    glBindTexture(GL_TEXTURE_2D, font_atlas_.texture_id);
    glTexImage2D(GL_TEXTURE_2D, 0, GL_ALPHA, atlas_size, atlas_size, 0, GL_ALPHA, GL_UNSIGNED_BYTE, atlas_bitmap.data());
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_LINEAR);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_LINEAR);
    glBindTexture(GL_TEXTURE_2D, 0);

    // Store character information
    for (int i = 0; i < 95; i++) {
        int char_index = i + 32; // ASCII offset
        if (char_index < 128) {
            stbtt_packedchar& pc = packed_chars[i];
            FontAtlas::CharInfo& ci = font_atlas_.chars[char_index];

            ci.x0 = pc.x0 / (float)atlas_size;
            ci.y0 = pc.y0 / (float)atlas_size;
            ci.x1 = pc.x1 / (float)atlas_size;
            ci.y1 = pc.y1 / (float)atlas_size;
            ci.xoff = pc.xoff;
            ci.yoff = pc.yoff;
            ci.xadvance = pc.xadvance;
        }
    }

    font_atlas_.loaded = true;
    std::cout << "Font loaded successfully" << std::endl;
    return true;
}

void OpenGLRenderer::DrawText(const std::string& text, const Vec2& position, const Color& color, float scale) {
    if (text.empty()) {
        return;
    }

    // Load font if not already loaded
    if (!font_atlas_.loaded) {
        if (!LoadFont(24.0f)) {  // Larger font size
            return; // Font loading failed
        }
    }

    // Enable texturing and blending
    glEnable(GL_TEXTURE_2D);
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);

    glBindTexture(GL_TEXTURE_2D, font_atlas_.texture_id);
    glColor4f(color.r, color.g, color.b, color.a);

    float x = position.x;
    float y = position.y;

    glBegin(GL_QUADS);

    // Get baseline information for proper positioning
    float baseline = font_atlas_.font_size * 0.6f * scale; // Reduced baseline offset

    for (char c : text) {
        if (c >= 32 && c < 128) {
            const FontAtlas::CharInfo& ci = font_atlas_.chars[c];

            float char_width = (ci.x1 - ci.x0) * font_atlas_.atlas_width * scale;
            float char_height = (ci.y1 - ci.y0) * font_atlas_.atlas_height * scale;

            float xpos = x + ci.xoff * scale;
            float ypos = y + baseline + ci.yoff * scale;

            // Only draw if character has visible content
            if (char_width > 0 && char_height > 0) {
                // Draw textured quad
                glTexCoord2f(ci.x0, ci.y0); glVertex2f(xpos, ypos);
                glTexCoord2f(ci.x1, ci.y0); glVertex2f(xpos + char_width, ypos);
                glTexCoord2f(ci.x1, ci.y1); glVertex2f(xpos + char_width, ypos + char_height);
                glTexCoord2f(ci.x0, ci.y1); glVertex2f(xpos, ypos + char_height);
            }

            x += ci.xadvance * scale;
        }
    }

    glEnd();

    glBindTexture(GL_TEXTURE_2D, 0);
    glDisable(GL_BLEND);
    glDisable(GL_TEXTURE_2D);
}

Vec2 OpenGLRenderer::GetTextSize(const std::string& text, float scale) {
    if (text.empty()) {
        return Vec2(0, 0);
    }

    // Load font if not already loaded
    if (!font_atlas_.loaded) {
        if (!LoadFont(24.0f)) {  // Same font size as DrawText
            return Vec2(0, 0);
        }
    }

    float width = 0.0f;
    float height = font_atlas_.font_size * scale;

    for (char c : text) {
        if (c >= 32 && c < 128) {
            const FontAtlas::CharInfo& ci = font_atlas_.chars[c];
            width += ci.xadvance * scale;
        }
    }

    return Vec2(width, height);
}
