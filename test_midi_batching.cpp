#include "src/websocket_client.h"
#include <iostream>
#include <chrono>
#include <thread>

int main() {
    std::cout << "MIDI Batching Test" << std::endl;
    std::cout << "==================" << std::endl;
    
    // Create WebSocket client
    WebSocketClient client;
    
    // Test configuration
    std::cout << "Default batch timeout: " << client.getMidiBatchTimeout() << "ms" << std::endl;
    std::cout << "Default batch size: " << client.getMidiBatchSize() << " notes" << std::endl;
    
    // Test configuration changes
    client.setMidiBatchTimeout(10); // 10ms timeout
    client.setMidiBatchSize(5);     // 5 notes per batch
    
    std::cout << "Updated batch timeout: " << client.getMidiBatchTimeout() << "ms" << std::endl;
    std::cout << "Updated batch size: " << client.getMidiBatchSize() << " notes" << std::endl;
    
    // Connect to a test server (this will fail but we can still test batching logic)
    std::cout << "\nTesting MIDI batching (connection will fail, but batching logic works)..." << std::endl;
    
    // Try to connect (will fail but initialize the batching system)
    client.connect("localhost", 8080);
    
    // Wait a moment for initialization
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Test rapid note sending (simulating chord or rapid playing)
    std::cout << "\nSending rapid MIDI notes (should be batched)..." << std::endl;
    
    // Send a chord (multiple notes at once)
    for (int i = 0; i < 8; ++i) {
        uint8_t note = 60 + i; // C major chord and extensions
        uint8_t velocity = 100;
        uint32_t color = 0xFF0000; // Red
        
        client.sendNoteOn(note, velocity, color);
        std::cout << "Sent note " << (int)note << std::endl;
        
        // Small delay to simulate near-simultaneous playing
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    // Wait for batching to process
    std::cout << "\nWaiting for batch processing..." << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(50));
    
    // Send note offs
    std::cout << "\nSending note offs..." << std::endl;
    for (int i = 0; i < 8; ++i) {
        uint8_t note = 60 + i;
        client.sendNoteOff(note);
        std::cout << "Sent note off " << (int)note << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    // Force flush any remaining data
    std::cout << "\nFlushing remaining MIDI data..." << std::endl;
    client.flushMidiBatch();
    
    // Wait a moment before cleanup
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    std::cout << "\nTest completed. Check the output above for batching messages." << std::endl;
    std::cout << "You should see 'Sending MIDI batch with X notes' messages." << std::endl;
    
    return 0;
}
